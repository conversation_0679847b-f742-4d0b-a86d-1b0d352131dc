class EmailListModel {
  final bool success;
  final String message;
  final List<EmailItem> emails;

  EmailListModel({
    required this.success,
    required this.message,
    required this.emails,
  });

  factory EmailListModel.fromJson(Map<String, dynamic> json) {
    return EmailListModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      emails: json['emails'] != null
          ? List<EmailItem>.from(
              json['emails'].map((email) => EmailItem.fromJson(email)))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'emails': emails.map((email) => email.toJson()).toList(),
    };
  }
}

class EmailItem {
  final String id;
  final String email;
  final String? fullName;
  final String? userName;
  final String? userType;
  final String? profilePicture;
  final bool isActive;

  EmailItem({
    required this.id,
    required this.email,
    this.fullName,
    this.userName,
    this.userType,
    this.profilePicture,
    this.isActive = true,
  });

  factory EmailItem.fromJson(Map<String, dynamic> json) {
    return EmailItem(
      id: json['id']?.toString() ?? json['userId']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      fullName: json['fullName']?.toString() ?? json['name']?.toString(),
      userName: json['userName']?.toString() ?? json['username']?.toString(),
      userType: json['userType']?.toString() ?? json['type']?.toString(),
      profilePicture: json['profilePicture']?.toString() ?? json['profilePic']?.toString(),
      isActive: json['isActive'] ?? json['active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'fullName': fullName,
      'userName': userName,
      'userType': userType,
      'profilePicture': profilePicture,
      'isActive': isActive,
    };
  }

  // Helper method to get display name
  String get displayName {
    if (fullName != null && fullName!.isNotEmpty) {
      return fullName!;
    } else if (userName != null && userName!.isNotEmpty) {
      return userName!;
    } else {
      return email.split('@').first; // Use email prefix as fallback
    }
  }

  // Helper method to get user type display
  String get userTypeDisplay {
    switch (userType?.toLowerCase()) {
      case 'traveler':
        return 'Traveler';
      case 'supporter':
        return 'Supporter';
      case 'admin':
        return 'Admin';
      default:
        return 'User';
    }
  }
}
