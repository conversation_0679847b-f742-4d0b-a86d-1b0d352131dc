class EndPoint {
  static const String baseUrl = "https://followsafe.runasp.net/Authentication/";

  // Supporter endpoints
  static const String addSupporter =
      "https://followsafe.runasp.net/addsupporter/add";
  static const String getSupportersList =
      "https://followsafe.runasp.net/addsupporter/mine";

  // Traveler endpoints
  static const String addTraveler =
      "https://followsafe.runasp.net/addtraveler/add";
  static const String getTravelersList =
      "https://followsafe.runasp.net/addtraveler/mine";

  // Email list endpoint
  static const String getAllEmails =
      "https://followsafe.runasp.net/users/emails";

  // Authentication endpoints
  static const String signUp = "${baseUrl}register";
  static const String confirmEmail = "${baseUrl}confirm-email";
  static const String signIn = "${baseUrl}login";
  static const String resendConfirmationEmail =
      "${baseUrl}resend-confirmation-email";
  static const String forgotPassword = "${baseUrl}forget-password";
  static const String resetPassword = "${baseUrl}reset-password";
  static const String adultType = "${baseUrl}adult-type";
  static const String checkOtp = "${baseUrl}Check_otp";

  // ✅ OTP Endpoints
  static const String sendOtp = "${baseUrl}send-otp";
  static const String verifyOtp = "${baseUrl}verify-otp";

  static String getUserDataEndPoint(String id) {
    return "${baseUrl}user/get-user/$id";
  }
}

class ApiKey {
  static const String adultType = "adultType";
  static const String status = "statusCode";
  static const String errorMessage = "ErrorMessage";
  static const String email = "email";
  static const String password = "password";
  static const String token = "token";
  static const String message = "message";
  static const String id = "userId";
  static const String name = "fullname";
  static const String phone = "phone";
  static const String ConfirmNewPassword = "ConfirmNewPassword";
  static const String newpassword = "newpassword";
  static const String location = "location";
  static const String profilePic = "profilePic";
  static const String userType = "userType";

  // ✅ OTP Keys
  static const String userId = "UserId";
  static const String code = "code";
}
