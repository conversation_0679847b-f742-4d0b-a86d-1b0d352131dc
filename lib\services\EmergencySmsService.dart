import 'dart:developer';
import 'package:url_launcher/url_launcher.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

class EmergencySmsService {
  static const String emergencyMessage = "I am in danger";

  // Send emergency SMS to all stored contacts
  static Future<bool> sendEmergencySMS() async {
    try {
      log('Starting emergency SMS sending process...');

      // Get phone numbers from local storage
      final phoneNumbers = await OfflineContactService.getPhoneNumbers();

      if (phoneNumbers.isEmpty) {
        log('No phone numbers found for emergency SMS');
        return false;
      }

      log('Found ${phoneNumbers.length} phone numbers for emergency SMS');

      // Get current user info for personalized message
      final userName = _getCurrentUserName();
      final personalizedMessage =
          userName != null ? "$emergencyMessage - $userName" : emergencyMessage;

      log('Opening SMS app with emergency message: $personalizedMessage');

      // Open SMS app with the first contact and message
      bool result = await _openSMSApp(phoneNumbers, personalizedMessage);

      // Log the emergency event locally
      await _logEmergencyEvent();

      if (result) {
        log('SMS app opened successfully for ${phoneNumbers.length} contacts');
      } else {
        log('Failed to open SMS app');
      }

      return result;
    } catch (e) {
      log('Error sending emergency SMS: $e');
      return false;
    }
  }

  // Open SMS app with emergency message
  static Future<bool> _openSMSApp(
      List<String> phoneNumbers, String message) async {
    try {
      if (phoneNumbers.isEmpty) return false;

      // Clean and validate phone numbers
      final validNumbers = phoneNumbers
          .map((number) => _cleanPhoneNumber(number))
          .where((number) => _isValidPhoneNumber(number))
          .toList();

      if (validNumbers.isEmpty) {
        log('No valid phone numbers found');
        return false;
      }

      // Create SMS URL with multiple recipients
      final recipients = validNumbers.join(',');
      final smsUrl = 'sms:$recipients?body=${Uri.encodeComponent(message)}';

      log('Opening SMS app with URL: $smsUrl');

      if (await canLaunchUrl(Uri.parse(smsUrl))) {
        await launchUrl(Uri.parse(smsUrl));
        log('SMS app opened successfully');
        return true;
      } else {
        log('Cannot launch SMS app');
        return false;
      }
    } catch (e) {
      log('Error opening SMS app: $e');
      return false;
    }
  }

  // Clean phone number (remove spaces, dashes, etc.)
  static String _cleanPhoneNumber(String phoneNumber) {
    return phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
  }

  // Validate phone number format
  static bool _isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return false;

    // Basic validation: should contain only digits and optional + at start
    final regex = RegExp(r'^\+?[1-9]\d{1,14}$');
    return regex.hasMatch(phoneNumber);
  }

  // Get current user name for personalized message
  static String? _getCurrentUserName() {
    try {
      return CacheHelper.getData(key: 'userName') ??
          CacheHelper.getData(key: 'user_name') ??
          CacheHelper.getData(key: 'name') ??
          CacheHelper.getData(key: ApiKey.name);
    } catch (e) {
      log('Error getting user name: $e');
      return null;
    }
  }

  // Log emergency event for tracking
  static Future<void> _logEmergencyEvent() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final userId = CacheHelper.getData(key: ApiKey.userId);

      // Save emergency event to local storage for later sync
      final emergencyEvents =
          CacheHelper.getData(key: 'emergency_events') ?? [];
      if (emergencyEvents is List) {
        emergencyEvents.add({
          'timestamp': timestamp,
          'userId': userId,
          'message': emergencyMessage,
          'contactsCount': await OfflineContactService.getContactsCount(),
        });

        await CacheHelper.saveData(
            key: 'emergency_events', value: emergencyEvents);
        log('Emergency event logged locally');
      }
    } catch (e) {
      log('Error logging emergency event: $e');
    }
  }

  // Check if SMS app is available
  static Future<bool> checkSMSAvailability() async {
    try {
      // Check if we can launch SMS URLs
      const testUrl = 'sms:';
      return await canLaunchUrl(Uri.parse(testUrl));
    } catch (e) {
      log('SMS availability check failed: $e');
      return false;
    }
  }

  // Get emergency message preview
  static String getEmergencyMessagePreview() {
    final userName = _getCurrentUserName();
    return userName != null
        ? "$emergencyMessage - $userName"
        : emergencyMessage;
  }
}
