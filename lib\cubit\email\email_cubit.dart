import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pro/repo/EmailRepository.dart';
import 'package:pro/models/EmailListModel.dart';
import 'email_state.dart';

class EmailCubit extends Cubit<EmailState> {
  final EmailRepository repository;
  
  // Cache for loaded emails
  List<EmailItem> _allEmails = [];
  String? _lastSearchQuery;
  String? _lastUserTypeFilter;

  EmailCubit(this.repository) : super(EmailInitial());

  /// Get all emails
  Future<void> getAllEmails({
    bool refresh = false,
    String? userType,
  }) async {
    try {
      if (refresh || _allEmails.isEmpty) {
        emit(EmailLoading());
      }

      log('Getting all emails - refresh: $refresh, userType: $userType');

      final result = await repository.getAllEmails(userType: userType);

      if (result.success) {
        _allEmails = result.emails;
        _lastUserTypeFilter = userType;
        
        emit(EmailSuccess(
          emailList: result,
          hasMore: false,
          currentOffset: 0,
        ));
        
        log('Successfully loaded ${result.emails.length} emails');
      } else {
        emit(EmailError(result.message));
      }
    } catch (e) {
      log('Error getting all emails: $e');
      emit(EmailError('Failed to load emails: $e'));
    }
  }

  /// Search emails
  Future<void> searchEmails(String query) async {
    try {
      if (query.trim().isEmpty) {
        // If query is empty, show all emails
        await getAllEmails(refresh: false);
        return;
      }

      emit(EmailSearchLoading());
      _lastSearchQuery = query;

      log('Searching emails with query: $query');

      final result = await repository.searchEmails(query);

      if (result.success) {
        emit(EmailSearchSuccess(
          searchResults: result,
          query: query,
        ));
        
        log('Found ${result.emails.length} emails for query: $query');
      } else {
        emit(EmailError(result.message));
      }
    } catch (e) {
      log('Error searching emails: $e');
      emit(EmailError('Failed to search emails: $e'));
    }
  }

  /// Get email suggestions for autocomplete
  Future<void> getEmailSuggestions(String query) async {
    try {
      if (query.trim().length < 2) {
        emit(EmailSuggestionsSuccess(suggestions: [], query: query));
        return;
      }

      emit(EmailSuggestionsLoading());

      log('Getting email suggestions for: $query');

      final suggestions = await repository.getEmailSuggestions(query);

      emit(EmailSuggestionsSuccess(
        suggestions: suggestions,
        query: query,
      ));
      
      log('Found ${suggestions.length} email suggestions');
    } catch (e) {
      log('Error getting email suggestions: $e');
      emit(EmailError('Failed to get email suggestions: $e'));
    }
  }

  /// Check if email exists
  Future<void> checkEmailExists(String email) async {
    try {
      if (email.trim().isEmpty) {
        return;
      }

      emit(EmailExistsCheckLoading());

      log('Checking if email exists: $email');

      final exists = await repository.checkEmailExists(email);

      emit(EmailExistsCheckSuccess(
        exists: exists,
        email: email,
      ));
      
      log('Email exists check result: $exists');
    } catch (e) {
      log('Error checking email existence: $e');
      emit(EmailError('Failed to check email: $e'));
    }
  }

  /// Filter emails by user type
  Future<void> filterByUserType(String? userType) async {
    try {
      log('Filtering emails by user type: $userType');

      if (userType == null || userType.isEmpty) {
        // Show all emails
        await getAllEmails(refresh: false);
        return;
      }

      emit(EmailLoading());

      final result = await repository.getEmailsByUserType(userType);

      if (result.success) {
        _lastUserTypeFilter = userType;
        
        emit(EmailSuccess(
          emailList: result,
          hasMore: false,
          currentOffset: 0,
        ));
        
        log('Filtered ${result.emails.length} emails by type: $userType');
      } else {
        emit(EmailError(result.message));
      }
    } catch (e) {
      log('Error filtering emails by user type: $e');
      emit(EmailError('Failed to filter emails: $e'));
    }
  }

  /// Load more emails (pagination)
  Future<void> loadMoreEmails() async {
    try {
      final currentState = state;
      if (currentState is! EmailSuccess || !currentState.hasMore) {
        return;
      }

      log('Loading more emails from offset: ${currentState.currentOffset}');

      final result = await repository.getPaginatedEmails(
        limit: 20,
        offset: currentState.currentOffset + 20,
        searchQuery: _lastSearchQuery,
        userType: _lastUserTypeFilter,
      );

      if (result.success) {
        // Combine existing emails with new ones
        final combinedEmails = [
          ...currentState.emailList.emails,
          ...result.emails,
        ];

        final combinedResult = EmailListModel(
          success: true,
          message: result.message,
          emails: combinedEmails,
        );

        emit(EmailSuccess(
          emailList: combinedResult,
          hasMore: result.emails.length >= 20, // Assume more if we got full page
          currentOffset: currentState.currentOffset + 20,
        ));
        
        log('Loaded ${result.emails.length} more emails');
      } else {
        emit(EmailError(result.message));
      }
    } catch (e) {
      log('Error loading more emails: $e');
      emit(EmailError('Failed to load more emails: $e'));
    }
  }

  /// Refresh emails
  Future<void> refreshEmails() async {
    await getAllEmails(refresh: true, userType: _lastUserTypeFilter);
  }

  /// Clear search and show all emails
  void clearSearch() {
    _lastSearchQuery = null;
    getAllEmails(refresh: false, userType: _lastUserTypeFilter);
  }

  /// Get cached emails
  List<EmailItem> get cachedEmails => _allEmails;

  /// Get last search query
  String? get lastSearchQuery => _lastSearchQuery;

  /// Get last user type filter
  String? get lastUserTypeFilter => _lastUserTypeFilter;
}
