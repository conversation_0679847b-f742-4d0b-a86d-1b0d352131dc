import 'package:pro/models/EmailListModel.dart';

abstract class EmailState {}

class EmailInitial extends EmailState {}

class EmailLoading extends EmailState {}

class EmailSuccess extends EmailState {
  final EmailListModel emailList;
  final bool hasMore;
  final int currentOffset;

  EmailSuccess({
    required this.emailList,
    this.hasMore = false,
    this.currentOffset = 0,
  });
}

class EmailError extends EmailState {
  final String message;

  EmailError(this.message);
}

class EmailSearchLoading extends EmailState {}

class EmailSearchSuccess extends EmailState {
  final EmailListModel searchResults;
  final String query;

  EmailSearchSuccess({
    required this.searchResults,
    required this.query,
  });
}

class EmailSuggestionsLoading extends EmailState {}

class EmailSuggestionsSuccess extends EmailState {
  final List<String> suggestions;
  final String query;

  EmailSuggestionsSuccess({
    required this.suggestions,
    required this.query,
  });
}

class EmailExistsCheckLoading extends EmailState {}

class EmailExistsCheckSuccess extends EmailState {
  final bool exists;
  final String email;

  EmailExistsCheckSuccess({
    required this.exists,
    required this.email,
  });
}
