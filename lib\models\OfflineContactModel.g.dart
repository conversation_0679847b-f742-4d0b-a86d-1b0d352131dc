// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'OfflineContactModel.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************





import 'package:hive/hive.dart';
import 'package:pro/models/OfflineContactModel.dart';

class OfflineContactModelAdapter extends TypeAdapter<OfflineContactModel> {
  @override
  final int typeId = 0;

  @override
  OfflineContactModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OfflineContactModel(
      id: fields[0] as String,
      name: fields[1] as String,
      phoneNumber: fields[2] as String,
      userType: fields[3] as String,
      lastUpdated: fields[4] as DateTime,
      userId: fields[5] as String,
    );
  }

  @override
  void write(BinaryWriter writer, OfflineContactModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.phoneNumber)
      ..writeByte(3)
      ..write(obj.userType)
      ..writeByte(4)
      ..write(obj.lastUpdated)
      ..writeByte(5)
      ..write(obj.userId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OfflineContactModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
