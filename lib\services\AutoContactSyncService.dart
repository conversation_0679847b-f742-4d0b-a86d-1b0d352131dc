import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/services/OfflineSyncService.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

/// Service for automatically syncing contacts when relationships change
class AutoContactSyncService {
  static final AutoContactSyncService _instance = AutoContactSyncService._internal();
  factory AutoContactSyncService() => _instance;
  AutoContactSyncService._internal();

  late final OfflineSyncService _syncService;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> init() async {
    if (_isInitialized) return;
    
    try {
      final dio = Dio();
      final apiConsumer = DioConsumer(dio: dio);
      _syncService = OfflineSyncService(api: apiConsumer);
      _isInitialized = true;
      log('AutoContactSyncService initialized successfully');
    } catch (e) {
      log('Error initializing AutoContactSyncService: $e');
      rethrow;
    }
  }

  /// Sync contacts after adding a supporter
  Future<bool> syncAfterAddingSupporter({
    required String supporterId,
    required String supporterName,
    String? supporterPhone,
    String? supporterEmail,
  }) async {
    try {
      log('Auto-syncing contacts after adding supporter: $supporterName');
      
      if (!_isInitialized) await init();
      
      // Force sync from server to get updated list
      final success = await _syncService.forceSyncContacts();
      
      if (success) {
        log('Successfully synced contacts after adding supporter');
        
        // If we have supporter's phone number, add it directly to local contacts
        if (supporterPhone != null && supporterPhone.isNotEmpty) {
          await _addContactDirectly(
            id: supporterId,
            name: supporterName,
            phoneNumber: supporterPhone,
            userType: 'supporter',
            email: supporterEmail,
          );
        }
        
        return true;
      } else {
        log('Failed to sync contacts after adding supporter');
        
        // Fallback: add contact directly if we have phone number
        if (supporterPhone != null && supporterPhone.isNotEmpty) {
          await _addContactDirectly(
            id: supporterId,
            name: supporterName,
            phoneNumber: supporterPhone,
            userType: 'supporter',
            email: supporterEmail,
          );
          return true;
        }
        
        return false;
      }
    } catch (e) {
      log('Error syncing contacts after adding supporter: $e');
      return false;
    }
  }

  /// Sync contacts after adding a traveler
  Future<bool> syncAfterAddingTraveler({
    required String travelerId,
    required String travelerName,
    String? travelerPhone,
    String? travelerEmail,
  }) async {
    try {
      log('Auto-syncing contacts after adding traveler: $travelerName');
      
      if (!_isInitialized) await init();
      
      // Force sync from server to get updated list
      final success = await _syncService.forceSyncContacts();
      
      if (success) {
        log('Successfully synced contacts after adding traveler');
        
        // If we have traveler's phone number, add it directly to local contacts
        if (travelerPhone != null && travelerPhone.isNotEmpty) {
          await _addContactDirectly(
            id: travelerId,
            name: travelerName,
            phoneNumber: travelerPhone,
            userType: 'traveler',
            email: travelerEmail,
          );
        }
        
        return true;
      } else {
        log('Failed to sync contacts after adding traveler');
        
        // Fallback: add contact directly if we have phone number
        if (travelerPhone != null && travelerPhone.isNotEmpty) {
          await _addContactDirectly(
            id: travelerId,
            name: travelerName,
            phoneNumber: travelerPhone,
            userType: 'traveler',
            email: travelerEmail,
          );
          return true;
        }
        
        return false;
      }
    } catch (e) {
      log('Error syncing contacts after adding traveler: $e');
      return false;
    }
  }

  /// Add contact directly to local storage
  Future<void> _addContactDirectly({
    required String id,
    required String name,
    required String phoneNumber,
    required String userType,
    String? email,
  }) async {
    try {
      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        log('Cannot add contact directly: User ID not found');
        return;
      }

      // Create new contact
      final contact = OfflineContactModel(
        id: id,
        name: name,
        phoneNumber: phoneNumber,
        userType: userType,
        lastUpdated: DateTime.now(),
        userId: currentUserId,
      );

      // Get existing contacts
      final existingContacts = await OfflineContactService.getContacts();
      
      // Check if contact already exists
      final existingIndex = existingContacts.indexWhere((c) => c.id == id);
      
      if (existingIndex >= 0) {
        // Update existing contact
        existingContacts[existingIndex] = contact;
        log('Updated existing contact: $name');
      } else {
        // Add new contact
        existingContacts.add(contact);
        log('Added new contact directly: $name ($phoneNumber)');
      }

      // Save updated contacts list
      await OfflineContactService.saveContacts(existingContacts);
      
    } catch (e) {
      log('Error adding contact directly: $e');
    }
  }

  /// Force sync all contacts (manual trigger)
  Future<bool> forceSyncAllContacts() async {
    try {
      log('Force syncing all contacts...');
      
      if (!_isInitialized) await init();
      
      return await _syncService.forceSyncContacts();
    } catch (e) {
      log('Error force syncing all contacts: $e');
      return false;
    }
  }

  /// Check if sync is needed and perform it
  Future<bool> syncIfNeeded() async {
    try {
      if (!_isInitialized) await init();
      
      return await _syncService.syncContactsFromServer();
    } catch (e) {
      log('Error checking sync: $e');
      return false;
    }
  }

  /// Get current user ID
  String? _getCurrentUserId() {
    return CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");
  }

  /// Get contacts count for display
  Future<int> getContactsCount() async {
    try {
      return await OfflineContactService.getContactsCount();
    } catch (e) {
      log('Error getting contacts count: $e');
      return 0;
    }
  }

  /// Get phone numbers for emergency SMS
  Future<List<String>> getPhoneNumbers() async {
    try {
      return await OfflineContactService.getPhoneNumbers();
    } catch (e) {
      log('Error getting phone numbers: $e');
      return [];
    }
  }
}
