import 'dart:developer'; // For logging
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/cubit/adult-type-cubit/adult_type_cubit.dart';
import 'package:pro/cubit/user_cubit.dart';
import 'package:pro/cubit/user_state.dart';
import 'package:pro/core/di/di.dart';
import 'package:pro/home/<USER>/mode_selector.dart';
import 'package:pro/home/<USER>/supporter.dart';
import 'package:pro/home/<USER>/traveler.dart';
import 'package:pro/home/<USER>/kid_mode.dart';

// Make sure to import Kid_home

class SignInListener extends StatelessWidget {
  const SignInListener({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
      listener: (context, state) async {
        if (state is SignInLoading) {
          _showLoading(context);
        } else {
          _hideLoading(context);

          if (state is SignInSuccess) {
            _showMessage(context, "Signed in successfully", Colors.green);

            log("Full UserModel Response: ${state.userModel}"); // Log full user model
            final userType =
                state.userModel.userType.toLowerCase(); // Get user type

            log("Extracted UserType: $userType"); // Log extracted user type

            await Future.delayed(const Duration(seconds: 1));

            if (context.mounted) {
              if (userType == "kid") {
                log("Navigating to Kid_home");
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => SafetyScreen()),
                );
              } else {
                // Try to get user ID from different sources
                log("DEBUG: Full user model: ${state.userModel}");

                // First try to get user ID from model
                var userId = state.userModel.userId;
                log("DEBUG: User ID from model: $userId");

                // If user ID is empty, try to get it from token
                if (userId.isEmpty) {
                  // Get token from cache
                  final token = CacheHelper.getData(key: ApiKey.token);
                  log("DEBUG: Token from cache: $token");

                  if (token != null && token.toString().isNotEmpty) {
                    try {
                      // Decode token
                      final decodedToken = JwtDecoder.decode(token.toString());
                      log("DEBUG: Decoded token: $decodedToken");

                      // Try to get user ID from 'sub' claim
                      if (decodedToken.containsKey('sub')) {
                        userId = decodedToken['sub'];
                        log("DEBUG: Extracted user ID from token 'sub' claim: $userId");

                        // Save user ID to cache with multiple keys for redundancy
                        await CacheHelper.saveData(
                            key: ApiKey.id, value: userId);
                        await CacheHelper.saveData(
                            key: ApiKey.userId, value: userId);
                        await CacheHelper.saveData(
                            key: "userId", value: userId);
                        await CacheHelper.saveData(
                            key: "UserId", value: userId);
                        await CacheHelper.saveData(key: "sub", value: userId);
                        await CacheHelper.saveData(
                            key: "current_user_id", value: userId);

                        log("DEBUG: Saved user ID to cache with multiple keys");
                      }
                    } catch (e) {
                      log("ERROR: Failed to decode token: $e");
                    }
                  }
                }

                // If we still don't have a valid user ID, try to get it from cache
                if (userId.isEmpty) {
                  // Try different keys that might contain the user ID
                  final possibleKeys = [
                    ApiKey.id,
                    ApiKey.userId,
                    "userId",
                    "UserId",
                    "sub",
                    "current_user_id"
                  ];

                  for (var key in possibleKeys) {
                    final cachedId = CacheHelper.getData(key: key);
                    if (cachedId != null && cachedId.toString().isNotEmpty) {
                      userId = cachedId.toString();
                      log("DEBUG: Found user ID in cache with key $key: $userId");

                      // Save user ID to cache with multiple keys for redundancy
                      await CacheHelper.saveData(
                          key: "current_user_id", value: userId);

                      break;
                    }
                  }
                }

                // If we still don't have a valid user ID, redirect to ModeSelector
                if (userId.isEmpty) {
                  log("ERROR: Could not find valid user ID. Redirecting to ModeSelector");

                  // Navigate to ModeSelector since we can't identify the user
                  if (context.mounted) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => BlocProvider(
                          create: (context) => getIt<AdultTypeCubit>(),
                          child: const ModeSelector(),
                        ),
                      ),
                    );
                  }
                  return; // Exit early
                }

                // We have a valid user ID, save it to cache for future use
                log("DEBUG: Using user ID: $userId");
                await CacheHelper.saveData(
                    key: "current_user_id", value: userId);

                final userModeKey = "user_mode_$userId";

                // Log some debug information
                log("DEBUG: Checking cache state");

                // For debugging purposes only
                log("DEBUG: Checking if user mode exists");

                // Check if user has a specific mode preference
                final hasUserMode = CacheHelper.containsKey(key: userModeKey);
                final userModeValue = CacheHelper.getData(key: userModeKey);

                log("DEBUG: User ID: $userId");
                log("DEBUG: User mode key: $userModeKey");
                log("DEBUG: Has saved mode preference: $hasUserMode");
                log("DEBUG: User mode value: $userModeValue");

                if (!hasUserMode) {
                  // This user doesn't have a saved mode preference
                  log("No saved mode preference for this user");

                  log("First login, navigating to ModeSelector");

                  // Check if context is still valid after async operations
                  if (context.mounted) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => BlocProvider(
                          create: (context) => getIt<AdultTypeCubit>(),
                          child: const ModeSelector(),
                        ),
                      ),
                    );
                  }
                } else {
                  // User has a saved mode preference, get it from cache
                  final savedMode = CacheHelper.getData(key: userModeKey);
                  log("User has saved mode preference: $savedMode");

                  // Check if context is still valid after async operations
                  if (context.mounted) {
                    if (savedMode == "traveler") {
                      log("Navigating to Traveler page based on saved preference");
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(builder: (context) => Traveler()),
                      );
                    } else if (savedMode == "supporter") {
                      log("Navigating to Supporter page based on saved preference");
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const Supporter()),
                      );
                    } else {
                      log("Invalid saved mode, navigating to ModeSelector");
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => BlocProvider(
                            create: (context) => getIt<AdultTypeCubit>(),
                            child: const ModeSelector(),
                          ),
                        ),
                      );
                    }
                  }
                }
              }
            }
          } else if (state is SignInFailure) {
            _showMessage(context, state.errMessage, Colors.red);
          }
        }
      },
      child: const SizedBox.shrink(),
    );
  }

  void _showLoading(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );
  }

  void _hideLoading(BuildContext context) {
    if (context.mounted &&
        Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  void _showMessage(BuildContext context, String message, Color color) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: color),
      );
    }
  }
}
