import 'dart:developer';
import 'package:workmanager/workmanager.dart';
import 'package:pro/services/OfflineSyncService.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/core/di/di.dart';
import 'package:dio/dio.dart';

class BackgroundSyncService {
  static const String syncTaskName = 'offline_contacts_sync';
  static const String syncTaskTag = 'sync_contacts';
  
  // Initialize WorkManager
  static Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to true for debugging
      );
      
      log('BackgroundSyncService initialized');
    } catch (e) {
      log('Error initializing BackgroundSyncService: $e');
    }
  }

  // Schedule periodic sync (every 48 hours)
  static Future<void> schedulePeriodicSync() async {
    try {
      // Cancel any existing sync tasks
      await Workmanager().cancelByTag(syncTaskTag);
      
      // Schedule new periodic task
      await Workmanager().registerPeriodicTask(
        syncTaskName,
        syncTaskName,
        frequency: const Duration(hours: 48),
        tag: syncTaskTag,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        backoffPolicy: BackoffPolicy.exponential,
        backoffPolicyDelay: const Duration(minutes: 15),
      );
      
      log('Periodic sync scheduled for every 48 hours');
    } catch (e) {
      log('Error scheduling periodic sync: $e');
    }
  }

  // Schedule one-time sync
  static Future<void> scheduleOneTimeSync({Duration? delay}) async {
    try {
      await Workmanager().registerOneOffTask(
        'one_time_sync_${DateTime.now().millisecondsSinceEpoch}',
        syncTaskName,
        tag: syncTaskTag,
        initialDelay: delay ?? const Duration(seconds: 10),
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
      );
      
      log('One-time sync scheduled');
    } catch (e) {
      log('Error scheduling one-time sync: $e');
    }
  }

  // Cancel all sync tasks
  static Future<void> cancelAllSyncTasks() async {
    try {
      await Workmanager().cancelByTag(syncTaskTag);
      log('All sync tasks cancelled');
    } catch (e) {
      log('Error cancelling sync tasks: $e');
    }
  }

  // Check if sync tasks are scheduled
  static Future<bool> isSyncScheduled() async {
    try {
      // WorkManager doesn't provide a direct way to check scheduled tasks
      // So we'll assume it's scheduled if we've called schedulePeriodicSync
      return true;
    } catch (e) {
      log('Error checking sync schedule: $e');
      return false;
    }
  }
}

// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      log('Background task started: $task');
      
      switch (task) {
        case BackgroundSyncService.syncTaskName:
          return await _performBackgroundSync();
        default:
          log('Unknown background task: $task');
          return false;
      }
    } catch (e) {
      log('Error in background task $task: $e');
      return false;
    }
  });
}

// Perform the actual background sync
Future<bool> _performBackgroundSync() async {
  try {
    log('Starting background sync...');
    
    // Initialize services
    await OfflineContactService.init();
    
    // Create API consumer for sync service
    final dio = Dio();
    final apiConsumer = DioConsumer(dio: dio);
    final syncService = OfflineSyncService(api: apiConsumer);
    
    // Perform sync
    final success = await syncService.syncContactsFromServer();
    
    if (success) {
      log('Background sync completed successfully');
    } else {
      log('Background sync failed');
    }
    
    return success;
  } catch (e) {
    log('Error in background sync: $e');
    return false;
  }
}

// Helper class for managing background sync lifecycle
class BackgroundSyncManager {
  static bool _isInitialized = false;
  
  // Initialize and start background sync
  static Future<void> start() async {
    if (_isInitialized) return;
    
    try {
      await BackgroundSyncService.initialize();
      await BackgroundSyncService.schedulePeriodicSync();
      
      _isInitialized = true;
      log('Background sync manager started');
    } catch (e) {
      log('Error starting background sync manager: $e');
    }
  }

  // Stop background sync
  static Future<void> stop() async {
    try {
      await BackgroundSyncService.cancelAllSyncTasks();
      _isInitialized = false;
      log('Background sync manager stopped');
    } catch (e) {
      log('Error stopping background sync manager: $e');
    }
  }

  // Restart background sync (useful after app updates)
  static Future<void> restart() async {
    try {
      await stop();
      await start();
      log('Background sync manager restarted');
    } catch (e) {
      log('Error restarting background sync manager: $e');
    }
  }

  // Trigger immediate sync
  static Future<void> triggerImmediateSync() async {
    try {
      await BackgroundSyncService.scheduleOneTimeSync(
        delay: const Duration(seconds: 5)
      );
      log('Immediate sync triggered');
    } catch (e) {
      log('Error triggering immediate sync: $e');
    }
  }
}
