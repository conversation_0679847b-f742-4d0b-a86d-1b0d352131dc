import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pro/cubit/email/email_cubit.dart';
import 'package:pro/cubit/email/email_state.dart';
import 'package:pro/models/EmailListModel.dart';
import 'package:pro/core/di/di.dart';

// Email Search Widget with Autocomplete
class EmailSearchWidget extends StatefulWidget {
  final Function(String) onEmailSelected;
  final String? hintText;
  final String? initialValue;

  const EmailSearchWidget({
    Key? key,
    required this.onEmailSelected,
    this.hintText,
    this.initialValue,
  }) : super(key: key);

  @override
  State<EmailSearchWidget> createState() => _EmailSearchWidgetState();
}

class _EmailSearchWidgetState extends State<EmailSearchWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _suggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<EmailCubit>(),
      child: Column(
        children: [
          BlocListener<EmailCubit, EmailState>(
            listener: (context, state) {
              if (state is EmailSuggestionsSuccess) {
                setState(() {
                  _suggestions = state.suggestions;
                  _showSuggestions = state.suggestions.isNotEmpty;
                });
              }
            },
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Enter email address...',
                prefixIcon: const Icon(Icons.email),
                suffixIcon: _controller.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          setState(() {
                            _showSuggestions = false;
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                if (value.length >= 2) {
                  context.read<EmailCubit>().getEmailSuggestions(value);
                } else {
                  setState(() {
                    _showSuggestions = false;
                  });
                }
              },
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  widget.onEmailSelected(value);
                  setState(() {
                    _showSuggestions = false;
                  });
                }
              },
            ),
          ),
          if (_showSuggestions && _suggestions.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = _suggestions[index];
                  return ListTile(
                    dense: true,
                    leading: const Icon(Icons.email, size: 16),
                    title: Text(suggestion),
                    onTap: () {
                      _controller.text = suggestion;
                      widget.onEmailSelected(suggestion);
                      setState(() {
                        _showSuggestions = false;
                      });
                      _focusNode.unfocus();
                    },
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

class EmailListPage extends StatefulWidget {
  final bool isSelectionMode;
  final Function(EmailItem)? onEmailSelected;
  final String? title;

  const EmailListPage({
    Key? key,
    this.isSelectionMode = false,
    this.onEmailSelected,
    this.title,
  }) : super(key: key);

  @override
  State<EmailListPage> createState() => _EmailListPageState();
}

class _EmailListPageState extends State<EmailListPage> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedUserType;

  final List<String> _userTypes = ['All', 'Traveler', 'Supporter', 'Admin'];

  @override
  void initState() {
    super.initState();
    // Load emails when page opens
    context.read<EmailCubit>().getAllEmails();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? 'Email List'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<EmailCubit>().refreshEmails();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              children: [
                // Search Field
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search emails...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              context.read<EmailCubit>().clearSearch();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      context.read<EmailCubit>().searchEmails(value);
                    } else {
                      context.read<EmailCubit>().clearSearch();
                    }
                  },
                ),
                const SizedBox(height: 12),
                // User Type Filter
                DropdownButtonFormField<String>(
                  value: _selectedUserType,
                  decoration: InputDecoration(
                    labelText: 'Filter by User Type',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  items: _userTypes.map((type) {
                    return DropdownMenuItem(
                      value: type == 'All' ? null : type.toLowerCase(),
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedUserType = value;
                    });
                    context.read<EmailCubit>().filterByUserType(value);
                  },
                ),
              ],
            ),
          ),
          // Email List
          Expanded(
            child: BlocBuilder<EmailCubit, EmailState>(
              builder: (context, state) {
                if (state is EmailLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: Colors.red),
                  );
                } else if (state is EmailError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading emails',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<EmailCubit>().refreshEmails();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                } else if (state is EmailSuccess ||
                    state is EmailSearchSuccess) {
                  final emails = state is EmailSuccess
                      ? state.emailList.emails
                      : (state as EmailSearchSuccess).searchResults.emails;

                  if (emails.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.email_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No emails found',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchController.text.isNotEmpty
                                ? 'Try adjusting your search'
                                : 'No emails available',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: emails.length,
                    padding: const EdgeInsets.all(16),
                    itemBuilder: (context, index) {
                      final email = emails[index];
                      return _buildEmailCard(email);
                    },
                  );
                }

                return const Center(
                  child: Text('Welcome! Search for emails above.'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailCard(EmailItem email) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: Colors.red[100],
          child:
              email.profilePicture != null && email.profilePicture!.isNotEmpty
                  ? ClipOval(
                      child: Image.network(
                        email.profilePicture!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.person,
                            color: Colors.red[700],
                          );
                        },
                      ),
                    )
                  : Icon(
                      Icons.person,
                      color: Colors.red[700],
                    ),
        ),
        title: Text(
          email.displayName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              email.email,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getUserTypeColor(email.userType),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    email.userTypeDisplay,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (!email.isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Inactive',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        trailing: widget.isSelectionMode
            ? const Icon(Icons.arrow_forward_ios, size: 16)
            : null,
        onTap: widget.isSelectionMode && widget.onEmailSelected != null
            ? () {
                widget.onEmailSelected!(email);
                Navigator.of(context).pop();
              }
            : null,
      ),
    );
  }

  Color _getUserTypeColor(String? userType) {
    switch (userType?.toLowerCase()) {
      case 'traveler':
        return Colors.blue;
      case 'supporter':
        return Colors.green;
      case 'admin':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
