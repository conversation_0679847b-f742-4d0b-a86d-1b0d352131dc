import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pro/core/di/di.dart';
import 'package:pro/cubit/sos/sos_cubit.dart';
import 'package:pro/cubit/sos/sos_state.dart';
import 'package:pro/models/SosNotificationModel.dart';
import 'package:pro/home/<USER>/profile_page.dart';
import 'package:pro/home/<USER>/settings_page_traveler.dart';
import 'package:pro/home/<USER>/timer_page.dart';
import 'package:pro/home/<USER>/traveler_supporter_menu_List.dart';
import 'package:pro/home/<USER>/nearby_places_map.dart';
import 'package:pro/services/notification_service.dart';
import 'package:pro/widgets/custom_bottom_bar.dart';
import 'package:pro/widgets/header_profile.dart';
import 'package:pro/widgets/EmergencySOSButton.dart';
import 'package:pro/widgets/EmergencySettingsPage.dart';
import 'package:pro/services/EmergencySmsService.dart';

class Traveler extends StatefulWidget {
  const Traveler({super.key});

  @override
  State<Traveler> createState() => _TravelerPageState();
}

class _TravelerPageState extends State<Traveler> {
  int currentIndex = 0; // Current active index in BottomNavigationBar

  @override
  void initState() {
    super.initState();
    // Initialize notification service
    final notificationService = getIt<NotificationService>();
    notificationService.initialize();

    // Clear old notifications on startup (optional)
    // notificationService.clearAllNotifications();
  }

  // Pages to display based on the selected tab
  final List<Widget> pages = [
    const Center(child: Text('')),
    SettingsScreen(),
    TravelersListWidget(),
    const TimerWidget(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> places = [
      {'name': 'Police station', 'image': 'assets/images/police_station.png'},
      {'name': 'Fire station', 'image': 'assets/images/fire_station.jpg'},
      {'name': 'Hospital', 'image': 'assets/images/hospital.jpeg'},
      {'name': 'Petrol station', 'image': 'assets/images/petrol_station.jpeg'},
      {'name': 'Repair Shop', 'image': 'assets/images/repair_shop.jpeg'},
      {'name': 'Airport', 'image': 'assets/images/airport.jpeg'},
    ];

    return BlocProvider(
      create: (context) {
        final sosCubit = getIt<SosCubit>();
        sosCubit.initialize();
        return sosCubit;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false, // Prevent overlap with keyboard
        body: currentIndex == 0
            ? SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(
                      bottom: 10), // Add padding at the bottom of the page
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(height: 40),
                      const HeaderProfile(
                          showNotifications: true, userType: 'traveler'),
                      const SizedBox(height: 40),
                      const Padding(
                        padding: EdgeInsets.only(right: 150),
                        child: Text(
                          'Send sos message',
                          style: TextStyle(
                              fontFamily: 'Poppins',
                              color: Color(0xff212429),
                              fontWeight: FontWeight.bold,
                              fontSize: 17),
                        ),
                      ),
                      const SizedBox(height: 25),
                      // Emergency SOS Button with both offline SMS and online notifications
                      const EmergencySOSButton(
                        size: 120,
                        // No need for onPressed callback anymore - button handles both SMS and notifications internally
                      ),
                      const SizedBox(height: 16),
                      // Emergency Settings Button
                      TextButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const EmergencySettingsPage(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.settings, size: 16),
                        label: const Text('Emergency Settings'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 25),
                      const Padding(
                        padding: EdgeInsets.only(right: 160),
                        child: Text(
                          'Weather updates',
                          style: TextStyle(
                              fontFamily: 'Poppins',
                              color: Color(0xff212429),
                              fontWeight: FontWeight.bold,
                              fontSize: 17),
                        ),
                      ),
                      const SizedBox(height: 25),
                      Container(
                        width: 340,
                        height: 140,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          image: const DecorationImage(
                            image: AssetImage('assets/images/image.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      const SizedBox(height: 25),
                      const Padding(
                        padding: EdgeInsets.only(right: 140),
                        child: Text(
                          'Assistants near you',
                          style: TextStyle(
                              fontFamily: 'Poppins',
                              color: Color(0xff212429),
                              fontWeight: FontWeight.bold,
                              fontSize: 17),
                        ),
                      ),
                      const SizedBox(height: 25),
                      SizedBox(
                        height: 140,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: places.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding:
                                  const EdgeInsets.only(right: 10.0, left: 10),
                              child: GestureDetector(
                                onTap: () {
                                  String selectedPlace = places[index]['name']!;
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => NearbyPlacesMap(
                                          placeType: selectedPlace),
                                    ),
                                  );
                                },
                                child: Container(
                                  width: 120,
                                  height: 140,
                                  decoration: BoxDecoration(
                                    color: const Color(0xffebeeef),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Column(
                                    children: [
                                      Container(
                                        width: 120,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          color: Colors.grey,
                                          image: DecorationImage(
                                            image: AssetImage(
                                                places[index]['image']!),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Text(
                                        places[index]['name']!,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontFamily: 'Poppins'),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : pages[
                currentIndex], // Navigate to other pages based on currentIndex
        bottomNavigationBar: CustomBottomBar(
          currentIndex: currentIndex,
          onTap: (index) {
            setState(() {
              currentIndex = index;
            });
          },
          icons: bottomBarIcons,
        ),
      ),
    );
  }
}

final List<IconData> bottomBarIcons = [
  Icons.home,
  Icons.settings,
  Icons.menu,
  Icons.timer,
  Icons.person,
];
