import 'dart:developer';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/models/EmailListModel.dart';
import 'package:pro/cache/CacheHelper.dart';

class EmailRepository {
  final DioConsumer api;

  EmailRepository({required this.api});

  /// Get all emails from the backend
  Future<EmailListModel> getAllEmails({
    String? searchQuery,
    String? userType,
    int? limit,
    int? offset,
  }) async {
    try {
      log("Getting all emails from backend");

      // Check if token exists
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token == null || token.toString().isEmpty) {
        throw Exception("Authentication token not found. Please log in again.");
      }

      // Prepare query parameters
      Map<String, dynamic> queryParams = {};
      
      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['search'] = searchQuery;
      }
      
      if (userType != null && userType.isNotEmpty) {
        queryParams['userType'] = userType;
      }
      
      if (limit != null && limit > 0) {
        queryParams['limit'] = limit;
      }
      
      if (offset != null && offset >= 0) {
        queryParams['offset'] = offset;
      }

      log("Email API query params: $queryParams");

      // Make API call
      final response = await api.get(
        EndPoint.getAllEmails,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      log("Email API response: $response");

      // Handle different response formats
      if (response is List) {
        // If response is directly a list of emails
        return EmailListModel(
          success: true,
          message: "Emails retrieved successfully",
          emails: List<EmailItem>.from(
            response.map((email) => EmailItem.fromJson(email))
          ),
        );
      } else if (response is Map<String, dynamic>) {
        // If response is wrapped in an object
        if (response.containsKey('emails') || response.containsKey('data')) {
          return EmailListModel.fromJson(response);
        } else {
          // If response contains email data directly
          return EmailListModel(
            success: response['success'] ?? true,
            message: response['message'] ?? "Emails retrieved successfully",
            emails: [EmailItem.fromJson(response)],
          );
        }
      }

      // If response format is unexpected, return empty list
      return EmailListModel(
        success: false,
        message: "Invalid response format",
        emails: [],
      );
    } catch (e) {
      log("Error getting emails: $e");
      throw Exception("Failed to get emails: $e");
    }
  }

  /// Search emails by query
  Future<EmailListModel> searchEmails(String query) async {
    try {
      log("Searching emails with query: $query");
      
      return await getAllEmails(searchQuery: query);
    } catch (e) {
      log("Error searching emails: $e");
      throw Exception("Failed to search emails: $e");
    }
  }

  /// Get emails by user type
  Future<EmailListModel> getEmailsByUserType(String userType) async {
    try {
      log("Getting emails by user type: $userType");
      
      return await getAllEmails(userType: userType);
    } catch (e) {
      log("Error getting emails by user type: $e");
      throw Exception("Failed to get emails by user type: $e");
    }
  }

  /// Get paginated emails
  Future<EmailListModel> getPaginatedEmails({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
    String? userType,
  }) async {
    try {
      log("Getting paginated emails - limit: $limit, offset: $offset");
      
      return await getAllEmails(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        userType: userType,
      );
    } catch (e) {
      log("Error getting paginated emails: $e");
      throw Exception("Failed to get paginated emails: $e");
    }
  }

  /// Check if email exists
  Future<bool> checkEmailExists(String email) async {
    try {
      log("Checking if email exists: $email");
      
      final result = await searchEmails(email);
      
      // Check if any email matches exactly
      final exactMatch = result.emails.any(
        (emailItem) => emailItem.email.toLowerCase() == email.toLowerCase()
      );
      
      log("Email exists check result: $exactMatch");
      return exactMatch;
    } catch (e) {
      log("Error checking email existence: $e");
      return false;
    }
  }

  /// Get email suggestions for autocomplete
  Future<List<String>> getEmailSuggestions(String query) async {
    try {
      log("Getting email suggestions for: $query");
      
      if (query.length < 2) {
        return []; // Don't search for very short queries
      }
      
      final result = await searchEmails(query);
      
      // Extract email addresses and limit to 10 suggestions
      final suggestions = result.emails
          .map((emailItem) => emailItem.email)
          .where((email) => email.toLowerCase().contains(query.toLowerCase()))
          .take(10)
          .toList();
      
      log("Found ${suggestions.length} email suggestions");
      return suggestions;
    } catch (e) {
      log("Error getting email suggestions: $e");
      return [];
    }
  }
}
