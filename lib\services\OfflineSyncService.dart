import 'dart:developer';
import 'dart:convert';
import 'package:pro/core/API/api_consumer.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

class OfflineSyncService {
  final ApiConsumer api;
  
  // API endpoint for getting offline contacts
  static const String offlineContactsEndpoint = 
      'https://followsafe.runasp.net/Offline/Supporters-Phones';

  OfflineSyncService({required this.api});

  // Sync contacts from server
  Future<bool> syncContactsFromServer() async {
    try {
      log('Starting contacts sync from server...');
      
      // Check if sync is needed
      final needsSync = await OfflineContactService.isSyncNeeded();
      if (!needsSync) {
        log('Sync not needed yet (less than 48 hours since last sync)');
        return true;
      }

      // Get current user ID
      final userId = _getCurrentUserId();
      if (userId == null) {
        log('User ID not found, cannot sync');
        return false;
      }

      // Check if user is authenticated
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token == null) {
        log('Authentication token not found, cannot sync');
        return false;
      }

      // Fetch contacts from API
      final response = await api.get(
        offlineContactsEndpoint,
        queryParameters: {'userId': userId},
      );

      if (response == null) {
        log('No response from server');
        return false;
      }

      log('Received response from server: $response');

      // Parse response and convert to OfflineContactModel
      final contacts = await _parseContactsResponse(response, userId);
      
      if (contacts.isEmpty) {
        log('No contacts received from server');
        // Still save empty list to update sync timestamp
        await OfflineContactService.saveContacts([]);
        return true;
      }

      // Save contacts to local storage
      await OfflineContactService.saveContacts(contacts);
      
      log('Successfully synced ${contacts.length} contacts from server');
      return true;
    } catch (e) {
      log('Error syncing contacts from server: $e');
      return false;
    }
  }

  // Parse API response and convert to OfflineContactModel list
  Future<List<OfflineContactModel>> _parseContactsResponse(
    dynamic response, 
    String userId
  ) async {
    try {
      final contacts = <OfflineContactModel>[];
      
      // Handle different response formats
      List<dynamic> contactsData = [];
      
      if (response is Map<String, dynamic>) {
        // If response is wrapped in an object
        if (response.containsKey('data')) {
          contactsData = response['data'] as List<dynamic>? ?? [];
        } else if (response.containsKey('contacts')) {
          contactsData = response['contacts'] as List<dynamic>? ?? [];
        } else if (response.containsKey('supporters')) {
          contactsData = response['supporters'] as List<dynamic>? ?? [];
        } else if (response.containsKey('travelers')) {
          contactsData = response['travelers'] as List<dynamic>? ?? [];
        } else {
          // Try to find any list in the response
          for (final value in response.values) {
            if (value is List) {
              contactsData = value;
              break;
            }
          }
        }
      } else if (response is List<dynamic>) {
        // If response is directly a list
        contactsData = response;
      }

      log('Parsing ${contactsData.length} contacts from response');

      for (final contactData in contactsData) {
        try {
          if (contactData is Map<String, dynamic>) {
            final contact = OfflineContactModel(
              id: contactData['id']?.toString() ?? 
                  contactData['userId']?.toString() ?? 
                  DateTime.now().millisecondsSinceEpoch.toString(),
              name: contactData['name']?.toString() ?? 
                    contactData['fullName']?.toString() ?? 
                    contactData['userName']?.toString() ?? 
                    'Unknown',
              phoneNumber: contactData['phoneNumber']?.toString() ?? 
                          contactData['phone']?.toString() ?? 
                          contactData['mobile']?.toString() ?? '',
              userType: contactData['userType']?.toString() ?? 
                       contactData['type']?.toString() ?? 
                       _determineUserType(contactData),
              lastUpdated: DateTime.now(),
              userId: userId,
            );

            // Only add contacts with valid phone numbers
            if (contact.phoneNumber.isNotEmpty) {
              contacts.add(contact);
              log('Added contact: ${contact.name} - ${contact.phoneNumber}');
            } else {
              log('Skipped contact ${contact.name} - no phone number');
            }
          }
        } catch (e) {
          log('Error parsing individual contact: $e');
        }
      }

      return contacts;
    } catch (e) {
      log('Error parsing contacts response: $e');
      return [];
    }
  }

  // Determine user type based on available data
  String _determineUserType(Map<String, dynamic> contactData) {
    // Check if there are any indicators of user type
    if (contactData.containsKey('isTraveler') && contactData['isTraveler'] == true) {
      return 'Traveler';
    }
    if (contactData.containsKey('isSupporter') && contactData['isSupporter'] == true) {
      return 'Supporter';
    }
    if (contactData.containsKey('role')) {
      return contactData['role'].toString();
    }
    
    // Default based on current user type (opposite)
    final currentUserType = CacheHelper.getData(key: 'userType') ?? 
                           CacheHelper.getData(key: ApiKey.userType);
    
    if (currentUserType == 'Traveler') {
      return 'Supporter';
    } else if (currentUserType == 'Supporter') {
      return 'Traveler';
    }
    
    return 'Unknown';
  }

  // Get current user ID
  String? _getCurrentUserId() {
    return CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");
  }

  // Force sync (ignore 48-hour rule)
  Future<bool> forceSyncContacts() async {
    try {
      log('Force syncing contacts...');
      
      // Temporarily clear last sync time to force sync
      await CacheHelper.removeData(key: 'last_sync_timestamp');
      
      return await syncContactsFromServer();
    } catch (e) {
      log('Error force syncing contacts: $e');
      return false;
    }
  }

  // Get sync status information
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      final lastSync = CacheHelper.getData(key: 'last_sync_timestamp');
      final contactsCount = await OfflineContactService.getContactsCount();
      final needsSync = await OfflineContactService.isSyncNeeded();
      
      return {
        'lastSync': lastSync,
        'contactsCount': contactsCount,
        'needsSync': needsSync,
        'lastSyncFormatted': lastSync != null 
            ? DateTime.parse(lastSync.toString()).toString()
            : 'Never',
      };
    } catch (e) {
      log('Error getting sync status: $e');
      return {
        'lastSync': null,
        'contactsCount': 0,
        'needsSync': true,
        'lastSyncFormatted': 'Error',
      };
    }
  }
}
