# نظام الاستغاثة الطارئة (Emergency SMS System)

## نظرة عامة
تم تطوير نظام استغاثة متقدم يسمح للمستخدمين (Travelers و Supporters) بإرسال رسائل استغاثة عبر SMS حتى في حالة عدم وجود اتصال بالإنترنت.

## المميزات الرئيسية

### 🚨 إرسال رسائل الاستغاثة
- رسالة ثابتة: "I am in danger"
- إرسال فوري لجميع جهات الاتصال المحفوظة
- يعمل بدون إنترنت
- واجهة مستخدم بسيطة وسريعة

### 📱 تخزين محلي آمن
- استخدام Hive لتخزين أرقام الهواتف محلياً
- تشفير البيانات المحلية
- فصل البيانات حسب المستخدم

### 🔄 مزامنة تلقائية
- مزامنة كل 48 ساعة مع السيرفر
- تحديث تلقائي للأرقام المتغيرة
- عمل في الخلفية باستخدام WorkManager

### 🎯 دعم نوعي المستخدمين
- **Travelers**: يرسلون للـ Supporters
- **Supporters**: يرسلون للـ Travelers

## الملفات المضافة

### النماذج (Models)
- `lib/models/OfflineContactModel.dart` - نموذج جهة الاتصال
- `lib/models/OfflineContactModel.g.dart` - Hive adapter (مُولد تلقائياً)

### الخدمات (Services)
- `lib/services/OfflineContactService.dart` - إدارة قاعدة البيانات المحلية
- `lib/services/EmergencySmsService.dart` - إرسال رسائل SMS
- `lib/services/OfflineSyncService.dart` - مزامنة البيانات مع السيرفر
- `lib/services/BackgroundSyncService.dart` - المزامنة في الخلفية

### واجهات المستخدم (UI)
- `lib/widgets/EmergencySOSButton.dart` - زر الاستغاثة
- `lib/widgets/EmergencySettingsPage.dart` - صفحة إعدادات الاستغاثة

## كيفية الاستخدام

### للمطورين

#### 1. التهيئة في main.dart
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await CacheHelper.init();
  
  // تهيئة نظام الاستغاثة
  await OfflineContactService.init();
  await BackgroundSyncManager.start();
  
  runApp(MyApp());
}
```

#### 2. إضافة زر الاستغاثة
```dart
EmergencySOSButton(
  size: 120,
  showContactsCount: true,
  onPressed: () {
    // إجراءات إضافية عند الضغط
  },
)
```

#### 3. إضافة صفحة الإعدادات
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const EmergencySettingsPage(),
  ),
);
```

### للمستخدمين

#### 1. إعداد جهات الاتصال
- افتح صفحة "Emergency Settings"
- اضغط على "Sync Contacts Now" لجلب الأرقام من السيرفر
- تأكد من وجود جهات اتصال للطوارئ

#### 2. إرسال رسالة استغاثة
- اضغط على زر SOS الأحمر
- أكد الإرسال في النافذة المنبثقة
- ستُرسل الرسالة لجميع جهات الاتصال فوراً

#### 3. إدارة الإعدادات
- عرض عدد جهات الاتصال
- فحص حالة المزامنة
- اختبار إرسال الرسائل

## التقنيات المستخدمة

### المكتبات الأساسية
- `hive: ^2.2.3` - قاعدة بيانات محلية
- `hive_flutter: ^1.1.0` - تكامل Hive مع Flutter
- `flutter_sms: ^2.3.3` - إرسال رسائل SMS
- `workmanager: ^0.5.2` - المهام في الخلفية
- `path_provider: ^2.1.1` - الوصول لمسارات الملفات

### المكتبات المساعدة
- `build_runner: ^2.4.7` - لتوليد الكود
- `hive_generator: ^2.0.1` - لتوليد Hive adapters

## API المطلوب

### نقطة النهاية (Endpoint)
```
GET https://followsafe.runasp.net/Offline/Supporters-Phones
```

### المعاملات المطلوبة
- `userId`: معرف المستخدم الحالي
- `Authorization`: رمز المصادقة في الـ header

### تنسيق الاستجابة المتوقع
```json
{
  "data": [
    {
      "id": "user123",
      "name": "Ahmed Ali",
      "phoneNumber": "+201234567890",
      "userType": "Supporter"
    }
  ]
}
```

## الأمان والخصوصية

### تشفير البيانات
- جميع البيانات المحلية مشفرة باستخدام Hive
- أرقام الهواتف محمية ومفصولة حسب المستخدم

### إدارة الصلاحيات
- طلب صلاحية SMS تلقائياً
- التحقق من الصلاحيات قبل الإرسال

### حماية البيانات
- عدم تخزين بيانات حساسة غير ضرورية
- مسح البيانات عند تسجيل الخروج

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. عدم إرسال الرسائل
- تأكد من وجود صلاحية SMS
- تحقق من وجود جهات اتصال
- تأكد من صحة أرقام الهواتف

#### 2. فشل المزامنة
- تحقق من الاتصال بالإنترنت
- تأكد من صحة رمز المصادقة
- راجع استجابة السيرفر

#### 3. عدم حفظ البيانات
- تأكد من تهيئة Hive بشكل صحيح
- تحقق من صلاحيات الكتابة
- راجع مسار التخزين

## التطوير المستقبلي

### مميزات مقترحة
- إضافة رسائل مخصصة
- دعم الرسائل الصوتية
- تتبع حالة التسليم
- إشعارات للمستقبلين

### تحسينات تقنية
- ضغط البيانات المحلية
- تحسين أداء المزامنة
- دعم المزيد من منصات الرسائل

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجل (logs)
3. تواصل مع فريق التطوير

---

**ملاحظة**: هذا النظام مصمم للاستخدام في حالات الطوارئ الحقيقية. يُرجى استخدامه بمسؤولية.
