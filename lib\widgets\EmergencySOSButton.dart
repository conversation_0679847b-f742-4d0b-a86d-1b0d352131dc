import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pro/services/EmergencySmsService.dart';
import 'package:pro/services/OfflineContactService.dart';

class EmergencySOSButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final double size;
  final bool showContactsCount;
  
  const EmergencySOSButton({
    Key? key,
    this.onPressed,
    this.size = 120.0,
    this.showContactsCount = true,
  }) : super(key: key);

  @override
  State<EmergencySOSButton> createState() => _EmergencySOSButtonState();
}

class _EmergencySOSButtonState extends State<EmergencySOSButton>
    with SingleTickerProviderStateMixin {
  bool _isPressed = false;
  bool _isSending = false;
  int _contactsCount = 0;
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _loadContactsCount();
    _setupAnimation();
  }

  void _setupAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat(reverse: true);
  }

  Future<void> _loadContactsCount() async {
    try {
      final count = await OfflineContactService.getContactsCount();
      if (mounted) {
        setState(() {
          _contactsCount = count;
        });
      }
    } catch (e) {
      log('Error loading contacts count: $e');
    }
  }

  Future<void> _handleSOSPress() async {
    if (_isSending) return;

    setState(() {
      _isPressed = true;
      _isSending = true;
    });

    // Haptic feedback
    HapticFeedback.heavyImpact();

    try {
      // Show confirmation dialog first
      final confirmed = await _showConfirmationDialog();
      
      if (!confirmed) {
        setState(() {
          _isPressed = false;
          _isSending = false;
        });
        return;
      }

      // Show sending dialog
      _showSendingDialog();

      // Send emergency SMS
      final success = await EmergencySmsService.sendEmergencySMS();

      // Hide sending dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show result
      await _showResultDialog(success);

      // Call custom callback if provided
      widget.onPressed?.call();

    } catch (e) {
      log('Error in SOS button press: $e');
      
      // Hide sending dialog if still showing
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      await _showErrorDialog(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isPressed = false;
          _isSending = false;
        });
      }
    }
  }

  Future<bool> _showConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red, size: 28),
              SizedBox(width: 8),
              Text('Emergency Alert'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Are you sure you want to send an emergency message?',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Message:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      EmergencySmsService.getEmergencyMessagePreview(),
                      style: const TextStyle(fontStyle: FontStyle.italic),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Will be sent to $_contactsCount contacts',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Send Emergency SMS'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  void _showSendingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Sending emergency SMS...'),
            ],
          ),
        );
      },
    );
  }

  Future<void> _showResultDialog(bool success) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                success ? Icons.check_circle : Icons.error,
                color: success ? Colors.green : Colors.red,
                size: 28,
              ),
              const SizedBox(width: 8),
              Text(success ? 'Success' : 'Failed'),
            ],
          ),
          content: Text(
            success
                ? 'Emergency SMS sent successfully to $_contactsCount contacts.'
                : 'Failed to send emergency SMS. Please try again or contact emergency services directly.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showErrorDialog(String error) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red, size: 28),
              SizedBox(width: 8),
              Text('Error'),
            ],
          ),
          content: Text('An error occurred: $error'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: GestureDetector(
                onTap: _handleSOSPress,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        _isPressed ? Colors.red[800]! : Colors.red[600]!,
                        _isPressed ? Colors.red[900]! : Colors.red[700]!,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: _isSending
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 3,
                          ),
                        )
                      : const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.warning,
                                color: Colors.white,
                                size: 40,
                              ),
                              SizedBox(height: 4),
                              Text(
                                'SOS',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ),
            );
          },
        ),
        if (widget.showContactsCount) ...[
          const SizedBox(height: 8),
          Text(
            '$_contactsCount contacts',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
