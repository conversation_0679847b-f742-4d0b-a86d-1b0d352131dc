import 'package:hive/hive.dart';

part 'OfflineContactModel.g.dart';

@HiveType(typeId: 0)
class OfflineContactModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String phoneNumber;

  @HiveField(3)
  final String userType; // 'Traveler' or 'Supporter'

  @HiveField(4)
  final DateTime lastUpdated;

  @HiveField(5)
  final String userId; // ID of the current user

  OfflineContactModel({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.userType,
    required this.lastUpdated,
    required this.userId,
  });

  factory OfflineContactModel.fromJson(Map<String, dynamic> json) {
    return OfflineContactModel(
      id: json['id'] ?? '',
      name: json['name'] ?? json['fullName'] ?? '',
      phoneNumber: json['phoneNumber'] ?? json['phone'] ?? '',
      userType: json['userType'] ?? '',
      lastUpdated: DateTime.now(),
      userId: json['userId'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'userType': userType,
      'lastUpdated': lastUpdated.toIso8601String(),
      'userId': userId,
    };
  }

  @override
  String toString() {
    return 'OfflineContactModel(id: $id, name: $name, phoneNumber: $phoneNumber, userType: $userType)';
  }
}
