import 'package:flutter/material.dart';
import 'dart:async';

import 'package:pro/widgets/header_profile.dart';

class TimerWidget extends StatefulWidget {
  const TimerWidget({super.key});

  @override
  _TimerWidgetState createState() => _TimerWidgetState();
}

class _TimerWidgetState extends State<TimerWidget> {
  int _seconds = 0;
  bool _isRunning = false;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
  }

  void _startTimer() {
    if (!_isRunning) {
      setState(() {
        _isRunning = true;
      });
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _seconds++;
        });

        // إعادة العد عند اكتمال الساعة
        if (_seconds >= 3600) {
          _seconds = 0;
        }

        // عرض التنبيه كل 10 دقائق
        if (_seconds % 10 == 0) {
          _stopTimer(); // إيقاف العداد مؤقتًا
          _showAlertDialog(); // عرض التنبيه
        }
      });
    }
  }

  void _stopTimer() {
    if (_isRunning) {
      setState(() {
        _isRunning = false;
      });
      _timer?.cancel();
    }
  }

  void _resumeTimer() {
    _startTimer();
  }

  String _formatTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  void _showAlertDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.favorite,
                color: Colors.blue,
                size: 40,
              ),
              const SizedBox(height: 10),
              const Text(
                "Hey Ayman, Are You ok?",
                style: TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _resumeTimer();
                    },
                    child: const Text(
                      "Yes",
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _resumeTimer();
                    },
                    child: const Text(
                      "No",
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 40,
          ),
          const HeaderProfile(),
          const SizedBox(
            height: 50,
          ),
          const Text(
            "Activate Safety Mode ",
            style: TextStyle(fontFamily: 'Poppins', fontSize: 16),
          ),
          const SizedBox(
            height: 60,
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              // إطار الدائرة الثابتة (رمادي)
              CustomPaint(
                size: const Size(250, 250),
                painter: CirclePainter(
                  color: Colors.grey,
                ),
              ),
              // إطار القوس المتحرك
              CustomPaint(
                size: const Size(250, 250),
                painter: ArcPainter(
                  progress: (_seconds % 360) / 360, // نسبة التقدم من الساعة
                  color: const Color(0xff193869), // نفس لون الزر
                ),
              ),
              // النص (العداد)
              Text(
                _formatTime(_seconds),
                style: const TextStyle(
                  fontSize: 20,
                  color: Color(0xff193869),
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                ),
              ),
            ],
          ),
          const SizedBox(height: 100),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // زر Start
              ElevatedButton(
                onPressed: _startTimer,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _isRunning ? Colors.grey : const Color(0xff193869),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  minimumSize: const Size(170, 25),
                ),
                child: const Text(
                  'Start',
                  style: TextStyle(
                      fontSize: 16, color: Colors.white, fontFamily: 'Poppins'),
                ),
              ),
              const SizedBox(width: 20),

              // زر Stop
              ElevatedButton(
                onPressed: _stopTimer,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _isRunning ? const Color(0xff193869) : Colors.grey,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  minimumSize: const Size(170, 25),
                ),
                child: const Text(
                  'Stop',
                  style: TextStyle(
                      fontSize: 16, color: Colors.white, fontFamily: 'Poppins'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// رسم إطار الدائرة الثابتة
class CirclePainter extends CustomPainter {
  final Color color;

  CirclePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 12
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// رسم القوس المتحرك
class ArcPainter extends CustomPainter {
  final double progress; // نسبة التقدم من الساعة (0.0 إلى 1.0)
  final Color color;

  ArcPainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 12
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    final startAngle = -3.14 / 2; // البداية (أعلى الدائرة)
    final sweepAngle = 2 * 3.14 * progress; // طول القوس بناءً على التقدم

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
