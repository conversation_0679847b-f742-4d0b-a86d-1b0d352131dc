import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/cubit/add-supporter-cubit/add_supporter_cubit.dart';
import 'package:pro/cubit/adult-type-cubit/adult_type_cubit.dart';
import 'package:pro/cubit/forgetCubit/forget_password_cubit.dart';
import 'package:pro/cubit/otp/otp_cubit.dart';
import 'package:pro/cubit/otp_check/otp_check_cubit.dart';
import 'package:pro/cubit/resetpassCubit/reset_password_cubit.dart';
import 'package:pro/cubit/sos/sos_cubit.dart';
import 'package:pro/cubit/traveler/traveler_cubit.dart';
import 'package:pro/repo/SosRepository.dart';
import 'package:pro/repo/SupporterRepository.dart';
import 'package:pro/repo/TravelerRepository.dart';
import 'package:pro/repo/adult_type_repository.dart';
import 'package:pro/repo/forgetrepo.dart';
import 'package:pro/repo/otp_check_repo.dart';
import 'package:pro/repo/otp_repo.dart';
import 'package:pro/repo/reset_password_repo.dart';
import 'package:pro/services/notification_service.dart';
import 'package:pro/services/signalr_service.dart';

final GetIt getIt = GetIt.instance;

void setupServiceLocator() {
  // Register Dio
  getIt.registerLazySingleton<Dio>(() => Dio());

  // Register DioConsumer
  getIt
      .registerLazySingleton<DioConsumer>(() => DioConsumer(dio: getIt<Dio>()));

  // // Register UserRepository
  // getIt.registerLazySingleton<UserRepository>(
  //     () => UserRepository(api: getIt<DioConsumer>()));

  // // Register UserCubit
  // getIt.registerFactory<UserCubit>(() => UserCubit(getIt<UserRepository>()));

  // // Register CacheHelper
  // getIt.registerLazySingleton<CacheHelper>(() => CacheHelper());

  // Register OtpRepository
  getIt.registerLazySingleton<OtpRepository>(
      () => OtpRepository(api: getIt<DioConsumer>()));

  // Register OtpCubit
  getIt.registerFactory<OtpCubit>(() => OtpCubit(getIt<OtpRepository>()));

  // forget
  getIt.registerLazySingleton<ForgetRepo>(
      () => ForgetRepo(api: getIt<DioConsumer>()));

  getIt.registerFactory<ForgetPasswordCubit>(
      () => ForgetPasswordCubit(getIt<ForgetRepo>()));

  getIt.registerLazySingleton<OtpCheckRepository>(
      () => OtpCheckRepository(api: getIt<DioConsumer>()));

  // Register OtpCubit
  getIt.registerFactory<OtpCheckCubit>(
      () => OtpCheckCubit(getIt<OtpCheckRepository>()));

  getIt.registerLazySingleton<ResetPasswordRepository>(
      () => ResetPasswordRepository(api: getIt<DioConsumer>()));

  getIt.registerFactory<ResetPasswordCubit>(
      () => ResetPasswordCubit(getIt<ResetPasswordRepository>()));

  // Register AdultTypeRepository
  getIt.registerLazySingleton<AdultTypeRepository>(() => AdultTypeRepository());

  // Register AdultTypeCubit
  getIt.registerFactory<AdultTypeCubit>(
      () => AdultTypeCubit(getIt<AdultTypeRepository>()));

  // Register SupporterRepository
  getIt.registerLazySingleton<SupporterRepository>(
    () => SupporterRepository(api: getIt<DioConsumer>()),
  );

  // Register AddSupporterCubit
  getIt.registerFactory<AddSupporterCubit>(
    () => AddSupporterCubit(getIt<SupporterRepository>()),
  );

  // Register TravelerRepository
  getIt.registerLazySingleton<TravelerRepository>(
    () => TravelerRepository(api: getIt<DioConsumer>()),
  );

  // Register TravelerCubit
  getIt.registerFactory<TravelerCubit>(
    () => TravelerCubit(travelerRepository: getIt<TravelerRepository>()),
  );

  // Register SignalR Service
  getIt.registerLazySingleton<SignalRService>(() => SignalRService());

  // Register Notification Service
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());

  // Register SOS Repository
  getIt.registerLazySingleton<SosRepository>(() => SosRepository(
        api: getIt<DioConsumer>(),
        signalRService: getIt<SignalRService>(),
      ));

  // Register SOS Cubit
  getIt.registerFactory<SosCubit>(() => SosCubit(
        sosRepository: getIt<SosRepository>(),
      ));
}
