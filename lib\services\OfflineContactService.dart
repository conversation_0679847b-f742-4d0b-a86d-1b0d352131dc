import 'dart:developer';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

class OfflineContactService {
  static const String _boxName = 'offline_contacts';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static Box<OfflineContactModel>? _box;

  // Initialize Hive and open the box
  static Future<void> init() async {
    try {
      await Hive.initFlutter();

      // Register the adapter if not already registered
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(OfflineContactModelAdapter());
      }

      _box = await Hive.openBox<OfflineContactModel>(_boxName);
      log('OfflineContactService initialized successfully');
    } catch (e) {
      log('Error initializing OfflineContactService: $e');
      rethrow;
    }
  }

  // Get the current user ID
  static String? _getCurrentUserId() {
    final userId1 = CacheHelper.getData(key: ApiKey.userId);
    final userId2 = CacheHelper.getData(key: "current_user_id");
    final userId3 = CacheHelper.getData(key: ApiKey.id);
    final userId4 = CacheHelper.getData(key: "userId");
    final userId5 = CacheHelper.getData(key: "UserId");
    final userId6 = CacheHelper.getData(key: "sub");

    log('DEBUG - Checking user IDs:');
    log('  ApiKey.userId: $userId1');
    log('  current_user_id: $userId2');
    log('  ApiKey.id: $userId3');
    log('  userId: $userId4');
    log('  UserId: $userId5');
    log('  sub: $userId6');

    final finalUserId =
        userId1 ?? userId2 ?? userId3 ?? userId4 ?? userId5 ?? userId6;
    log('  Final userId: $finalUserId');

    return finalUserId;
  }

  // Save contacts to local storage
  static Future<void> saveContacts(List<OfflineContactModel> contacts) async {
    try {
      if (_box == null) await init();

      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      log('DEBUG - Saving ${contacts.length} contacts for user $currentUserId');

      // Clear existing contacts for current user
      await clearContactsForCurrentUser();

      // Save new contacts
      for (final contact in contacts) {
        final key = '${currentUserId}_${contact.id}';
        await _box!.put(key, contact);
        log('DEBUG - Saved contact with key: $key, name: ${contact.name}, phone: ${contact.phoneNumber}');
      }

      // Update last sync timestamp
      await _updateLastSyncTime();

      log('Saved ${contacts.length} contacts for user $currentUserId');

      // Verify save by reading back
      final savedContacts = await getContacts();
      log('DEBUG - Verification: Found ${savedContacts.length} contacts after save');
    } catch (e) {
      log('Error saving contacts: $e');
      rethrow;
    }
  }

  // Get all contacts for current user
  static Future<List<OfflineContactModel>> getContacts() async {
    try {
      if (_box == null) await init();

      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        log('User ID not found, returning empty contacts list');
        return [];
      }

      final contacts = <OfflineContactModel>[];

      log('DEBUG - Searching for contacts with userId: $currentUserId');
      log('DEBUG - Total keys in box: ${_box!.keys.length}');

      for (final key in _box!.keys) {
        log('DEBUG - Checking key: $key');
        if (key.toString().startsWith('${currentUserId}_')) {
          final contact = _box!.get(key);
          if (contact != null) {
            contacts.add(contact);
            log('DEBUG - Found contact: ${contact.name} (${contact.phoneNumber})');
          }
        }
      }

      log('Retrieved ${contacts.length} contacts for user $currentUserId');
      return contacts;
    } catch (e) {
      log('Error getting contacts: $e');
      return [];
    }
  }

  // Get phone numbers only for SMS sending
  static Future<List<String>> getPhoneNumbers() async {
    try {
      final contacts = await getContacts();
      final phoneNumbers = contacts
          .map((contact) => contact.phoneNumber)
          .where((phone) => phone.isNotEmpty)
          .toList();

      log('Retrieved ${phoneNumbers.length} phone numbers for SMS');
      return phoneNumbers;
    } catch (e) {
      log('Error getting phone numbers: $e');
      return [];
    }
  }

  // Clear contacts for current user
  static Future<void> clearContactsForCurrentUser() async {
    try {
      if (_box == null) await init();

      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) return;

      final keysToDelete = <String>[];
      for (final key in _box!.keys) {
        if (key.toString().startsWith('${currentUserId}_')) {
          keysToDelete.add(key.toString());
        }
      }

      for (final key in keysToDelete) {
        await _box!.delete(key);
      }

      log('Cleared contacts for user $currentUserId');
    } catch (e) {
      log('Error clearing contacts: $e');
    }
  }

  // Check if sync is needed (48 hours)
  static Future<bool> isSyncNeeded() async {
    try {
      final lastSync = CacheHelper.getData(key: _lastSyncKey);
      if (lastSync == null) return true;

      final lastSyncTime = DateTime.parse(lastSync.toString());
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);

      final needsSync = difference.inHours >= 48;
      log('Last sync: $lastSyncTime, Hours since: ${difference.inHours}, Needs sync: $needsSync');

      return needsSync;
    } catch (e) {
      log('Error checking sync status: $e');
      return true; // Default to needing sync if error
    }
  }

  // Update last sync timestamp
  static Future<void> _updateLastSyncTime() async {
    try {
      await CacheHelper.saveData(
        key: _lastSyncKey,
        value: DateTime.now().toIso8601String(),
      );
      log('Updated last sync timestamp');
    } catch (e) {
      log('Error updating sync timestamp: $e');
    }
  }

  // Get contacts count for current user
  static Future<int> getContactsCount() async {
    try {
      final contacts = await getContacts();

      // If no contacts found with current user ID, try to find any contacts
      if (contacts.isEmpty) {
        log('DEBUG - No contacts found with current user ID, checking all contacts...');
        if (_box != null) {
          final allContacts = <OfflineContactModel>[];
          for (final key in _box!.keys) {
            final contact = _box!.get(key);
            if (contact != null) {
              allContacts.add(contact);
              log('DEBUG - Found contact with key: $key, name: ${contact.name}');
            }
          }
          log('DEBUG - Total contacts in box (any user): ${allContacts.length}');

          // If we found contacts but they don't match current user ID,
          // let's return the count anyway for now
          if (allContacts.isNotEmpty) {
            log('DEBUG - Returning count of all contacts: ${allContacts.length}');
            return allContacts.length;
          }
        }
      }

      return contacts.length;
    } catch (e) {
      log('Error getting contacts count: $e');
      return 0;
    }
  }

  // Fix user ID mismatch by updating all contacts to current user ID
  static Future<void> fixUserIdMismatch() async {
    try {
      if (_box == null) await init();

      final currentUserId = _getCurrentUserId();
      if (currentUserId == null) {
        log('Cannot fix user ID mismatch: current user ID not found');
        return;
      }

      log('DEBUG - Fixing user ID mismatch for user: $currentUserId');

      final allContacts = <OfflineContactModel>[];
      final keysToDelete = <String>[];

      // Collect all contacts and keys to delete
      for (final key in _box!.keys) {
        final contact = _box!.get(key);
        if (contact != null) {
          // Update contact's userId to current user
          final updatedContact = OfflineContactModel(
            id: contact.id,
            name: contact.name,
            phoneNumber: contact.phoneNumber,
            userType: contact.userType,
            lastUpdated: contact.lastUpdated,
            userId: currentUserId, // Update to current user ID
          );
          allContacts.add(updatedContact);
          keysToDelete.add(key.toString());
        }
      }

      // Delete old keys
      for (final key in keysToDelete) {
        await _box!.delete(key);
      }

      // Save contacts with correct user ID
      for (final contact in allContacts) {
        final newKey = '${currentUserId}_${contact.id}';
        await _box!.put(newKey, contact);
      }

      log('DEBUG - Fixed ${allContacts.length} contacts with correct user ID');
    } catch (e) {
      log('Error fixing user ID mismatch: $e');
    }
  }

  // Close the box (call when app is closing)
  static Future<void> close() async {
    try {
      await _box?.close();
      log('OfflineContactService closed');
    } catch (e) {
      log('Error closing OfflineContactService: $e');
    }
  }
}
