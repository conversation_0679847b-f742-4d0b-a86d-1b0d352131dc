import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/services/EmergencySmsService.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Emergency System Tests', () {
    setUpAll(() async {
      // Initialize test environment
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      await CacheHelper.init();
      
      // Initialize Hive for testing
      await Hive.initFlutter();
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(OfflineContactModelAdapter());
      }
    });

    tearDownAll(() async {
      // Clean up after tests
      await Hive.deleteFromDisk();
    });

    group('OfflineContactService Tests', () {
      test('should initialize successfully', () async {
        await OfflineContactService.init();
        expect(true, true); // If no exception, test passes
      });

      test('should save and retrieve contacts', () async {
        await OfflineContactService.init();
        
        // Create test contacts
        final testContacts = [
          OfflineContactModel(
            id: 'test1',
            name: 'Test Contact 1',
            phoneNumber: '+201234567890',
            userType: 'Supporter',
            lastUpdated: DateTime.now(),
            userId: 'testUser',
          ),
          OfflineContactModel(
            id: 'test2',
            name: 'Test Contact 2',
            phoneNumber: '+201234567891',
            userType: 'Traveler',
            lastUpdated: DateTime.now(),
            userId: 'testUser',
          ),
        ];

        // Mock user ID
        await CacheHelper.saveData(key: 'userId', value: 'testUser');

        // Save contacts
        await OfflineContactService.saveContacts(testContacts);

        // Retrieve contacts
        final retrievedContacts = await OfflineContactService.getContacts();

        expect(retrievedContacts.length, 2);
        expect(retrievedContacts[0].name, 'Test Contact 1');
        expect(retrievedContacts[1].phoneNumber, '+201234567891');
      });

      test('should get phone numbers only', () async {
        await OfflineContactService.init();
        
        // Mock user ID
        await CacheHelper.saveData(key: 'userId', value: 'testUser');

        final phoneNumbers = await OfflineContactService.getPhoneNumbers();
        
        expect(phoneNumbers, isA<List<String>>());
        // Should contain the phone numbers from previous test
        expect(phoneNumbers.length, greaterThanOrEqualTo(0));
      });

      test('should check sync status', () async {
        final needsSync = await OfflineContactService.isSyncNeeded();
        expect(needsSync, isA<bool>());
      });

      test('should get contacts count', () async {
        final count = await OfflineContactService.getContactsCount();
        expect(count, isA<int>());
        expect(count, greaterThanOrEqualTo(0));
      });
    });

    group('EmergencySmsService Tests', () {
      test('should generate emergency message preview', () {
        final message = EmergencySmsService.getEmergencyMessagePreview();
        expect(message, contains('I am in danger'));
      });

      test('should check SMS permission', () async {
        final hasPermission = await EmergencySmsService.checkSMSPermission();
        expect(hasPermission, isA<bool>());
      });

      test('should validate phone numbers', () {
        // Test valid phone numbers
        expect(EmergencySmsService.getEmergencyMessagePreview(), isNotEmpty);
      });
    });

    group('OfflineContactModel Tests', () {
      test('should create model from JSON', () {
        final json = {
          'id': 'test123',
          'name': 'John Doe',
          'phoneNumber': '+201234567890',
          'userType': 'Supporter',
          'userId': 'user123',
        };

        final contact = OfflineContactModel.fromJson(json);

        expect(contact.id, 'test123');
        expect(contact.name, 'John Doe');
        expect(contact.phoneNumber, '+201234567890');
        expect(contact.userType, 'Supporter');
        expect(contact.userId, 'user123');
      });

      test('should convert model to JSON', () {
        final contact = OfflineContactModel(
          id: 'test123',
          name: 'John Doe',
          phoneNumber: '+201234567890',
          userType: 'Supporter',
          lastUpdated: DateTime.now(),
          userId: 'user123',
        );

        final json = contact.toJson();

        expect(json['id'], 'test123');
        expect(json['name'], 'John Doe');
        expect(json['phoneNumber'], '+201234567890');
        expect(json['userType'], 'Supporter');
        expect(json['userId'], 'user123');
        expect(json['lastUpdated'], isA<String>());
      });

      test('should handle missing JSON fields gracefully', () {
        final json = {
          'id': 'test123',
          // Missing name, should default to empty string
          'phoneNumber': '+201234567890',
        };

        final contact = OfflineContactModel.fromJson(json);

        expect(contact.id, 'test123');
        expect(contact.name, ''); // Should default to empty string
        expect(contact.phoneNumber, '+201234567890');
      });
    });

    group('Integration Tests', () {
      test('should complete full emergency flow', () async {
        // Initialize services
        await OfflineContactService.init();
        
        // Mock user data
        await CacheHelper.saveData(key: 'userId', value: 'testUser');
        await CacheHelper.saveData(key: 'userName', value: 'Test User');

        // Create and save test contacts
        final testContacts = [
          OfflineContactModel(
            id: 'emergency1',
            name: 'Emergency Contact 1',
            phoneNumber: '+201234567890',
            userType: 'Supporter',
            lastUpdated: DateTime.now(),
            userId: 'testUser',
          ),
        ];

        await OfflineContactService.saveContacts(testContacts);

        // Verify contacts are saved
        final savedContacts = await OfflineContactService.getContacts();
        expect(savedContacts.length, 1);

        // Get phone numbers for SMS
        final phoneNumbers = await OfflineContactService.getPhoneNumbers();
        expect(phoneNumbers.length, 1);
        expect(phoneNumbers[0], '+201234567890');

        // Test emergency message generation
        final message = EmergencySmsService.getEmergencyMessagePreview();
        expect(message, contains('I am in danger'));
        expect(message, contains('Test User'));
      });

      test('should handle empty contacts gracefully', () async {
        await OfflineContactService.init();
        
        // Clear all contacts
        await OfflineContactService.clearContactsForCurrentUser();
        
        // Try to get phone numbers
        final phoneNumbers = await OfflineContactService.getPhoneNumbers();
        expect(phoneNumbers, isEmpty);

        // Contacts count should be 0
        final count = await OfflineContactService.getContactsCount();
        expect(count, 0);
      });
    });

    group('Error Handling Tests', () {
      test('should handle invalid user ID gracefully', () async {
        // Clear user ID
        await CacheHelper.removeData(key: 'userId');
        
        // Try to get contacts without user ID
        final contacts = await OfflineContactService.getContacts();
        expect(contacts, isEmpty);
      });

      test('should handle sync timestamp errors', () async {
        // This should not throw an exception
        final needsSync = await OfflineContactService.isSyncNeeded();
        expect(needsSync, true); // Should default to true if no timestamp
      });
    });
  });
}

// Helper function to create test contact
OfflineContactModel createTestContact({
  String id = 'test',
  String name = 'Test Contact',
  String phoneNumber = '+201234567890',
  String userType = 'Supporter',
  String userId = 'testUser',
}) {
  return OfflineContactModel(
    id: id,
    name: name,
    phoneNumber: phoneNumber,
    userType: userType,
    lastUpdated: DateTime.now(),
    userId: userId,
  );
}
