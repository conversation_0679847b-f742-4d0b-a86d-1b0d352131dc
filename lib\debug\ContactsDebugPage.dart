import 'package:flutter/material.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/models/OfflineContactModel.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'dart:developer';

class ContactsDebugPage extends StatefulWidget {
  const ContactsDebugPage({Key? key}) : super(key: key);

  @override
  State<ContactsDebugPage> createState() => _ContactsDebugPageState();
}

class _ContactsDebugPageState extends State<ContactsDebugPage> {
  List<OfflineContactModel> _contacts = [];
  int _contactsCount = 0;
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all debug information
      final contacts = await OfflineContactService.getContacts();
      final count = await OfflineContactService.getContactsCount();

      // Get user ID info
      final userId1 = CacheHelper.getData(key: ApiKey.userId);
      final userId2 = CacheHelper.getData(key: "current_user_id");
      final userId3 = CacheHelper.getData(key: ApiKey.id);
      final userId4 = CacheHelper.getData(key: "userId");
      final userId5 = CacheHelper.getData(key: "UserId");
      final userId6 = CacheHelper.getData(key: "sub");

      final debugInfo = '''
DEBUG INFORMATION:

User IDs:
- ApiKey.userId: $userId1
- current_user_id: $userId2
- ApiKey.id: $userId3
- userId: $userId4
- UserId: $userId5
- sub: $userId6

Contacts:
- Count: $count
- Actual contacts found: ${contacts.length}

Contact Details:
${contacts.map((c) => '- ${c.name}: ${c.phoneNumber} (ID: ${c.id}, Type: ${c.userType})').join('\n')}
''';

      setState(() {
        _contacts = contacts;
        _contactsCount = count;
        _debugInfo = debugInfo;
      });
    } catch (e) {
      setState(() {
        _debugInfo = 'Error loading debug info: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addTestContact() async {
    try {
      final testContact = OfflineContactModel(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Test Contact',
        phoneNumber: '+1234567890',
        userType: 'supporter',
        lastUpdated: DateTime.now(),
        userId: 'test_user',
      );

      final existingContacts = await OfflineContactService.getContacts();
      existingContacts.add(testContact);

      await OfflineContactService.saveContacts(existingContacts);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test contact added!'),
          backgroundColor: Colors.green,
        ),
      );

      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error adding test contact: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _clearAllContacts() async {
    try {
      await OfflineContactService.saveContacts([]);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All contacts cleared!'),
          backgroundColor: Colors.orange,
        ),
      );

      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error clearing contacts: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _fixUserIdMismatch() async {
    try {
      await OfflineContactService.fixUserIdMismatch();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User ID mismatch fixed!'),
            backgroundColor: Colors.blue,
          ),
        );
      }

      await _loadDebugInfo();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fixing user ID mismatch: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contacts Debug'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Summary Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Summary',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text('Contacts Count: $_contactsCount'),
                          Text('Actual Contacts: ${_contacts.length}'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _addTestContact,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Add Test Contact'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _clearAllContacts,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Clear All'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Fix User ID Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _fixUserIdMismatch,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Fix User ID Mismatch'),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Debug Info Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Debug Information',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _debugInfo,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Contacts List
                  if (_contacts.isNotEmpty) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Contacts List',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            ..._contacts.map((contact) => ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Colors.blue,
                                    child: Text(contact.name[0].toUpperCase()),
                                  ),
                                  title: Text(contact.name),
                                  subtitle: Text(
                                      '${contact.phoneNumber}\nType: ${contact.userType}'),
                                  trailing: Text('ID: ${contact.id}'),
                                  isThreeLine: true,
                                )),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
