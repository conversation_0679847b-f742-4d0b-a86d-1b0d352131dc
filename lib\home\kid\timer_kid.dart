import 'package:flutter/material.dart';
import 'dart:async';

class TimerWidgetKid extends StatefulWidget {
  const TimerWidgetKid({super.key});

  @override
  _TimerWidgetKidState createState() => _TimerWidgetKidState();
}

class _TimerWidgetKidState extends State<TimerWidgetKid> {
  int _seconds = 0;
  bool _isRunning = false;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
  }

  void _startTimer() {
    if (!_isRunning) {
      setState(() {
        _isRunning = true;
      });
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _seconds++;
        });
      });
    }
  }

  void _stopTimer() {
    if (_isRunning) {
      setState(() {
        _isRunning = false;
      });
      _timer?.cancel();
    }
  }

  String _formatTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.topCenter,
        children: [
          Column(
            children: [
              // تعديل ارتفاع الـ Container الأبيض العلوي
              Container(
                height: 120, // تم تقليل الارتفاع من 180 إلى 120
                color: Colors.white,
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const SizedBox(
                      width: 80,
                    ),
                    const Text(
                      'Safety Mode',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xff00ff88), Color(0xff00d2ff)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: Column(
                    children: [
                      const Spacer(flex: 1), // مساحة علوية
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          CustomPaint(
                            size: const Size(300, 300), // زيادة حجم الدائرة
                            painter: CirclePainter(color: Colors.white),
                          ),
                          CustomPaint(
                            size: const Size(
                                300, 300), // زيادة حجم القطاع الدائري
                            painter: ArcPainter(
                              progress: (_seconds % 3600) / 3600,
                              color: const Color(0xff193869),
                            ),
                          ),
                          Text(
                            _formatTime(_seconds),
                            style: const TextStyle(
                              fontSize: 24, // زيادة حجم النص
                              color: Color(0xff193869),
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 40),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: _isRunning ? null : _startTimer,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  _isRunning ? Colors.grey : Colors.green,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              fixedSize:
                                  const Size(140, 40), // زيادة حجم الأزرار
                              elevation: 5,
                            ),
                            child: const Text(
                              'Activate',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ),
                          const SizedBox(width: 20),
                          ElevatedButton(
                            onPressed: _isRunning ? _stopTimer : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  _isRunning ? Colors.red : Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                              fixedSize:
                                  const Size(140, 40), // زيادة حجم الأزرار
                              side: BorderSide(
                                  color: _isRunning ? Colors.red : Colors.grey),
                              elevation: 5,
                            ),
                            child: Text(
                              'Stop',
                              style: TextStyle(
                                fontSize: 16,
                                color: _isRunning ? Colors.white : Colors.black,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(flex: 2), // مساحة سفلية
                    ],
                  ),
                ),
              ),
            ],
          ),
          // إضافة مسافة من الأعلى للصورة
          Positioned(
            top: 80, // تم تعديل المسافة من الأعلى من 140 إلى 80
            left: 0,
            right: 0,
            child: Center(
              child: ClipOval(
                child: Image.asset(
                  "assets/images/girl.png",
                  width: 100,
                  height: 100,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CirclePainter extends CustomPainter {
  final Color color;

  CirclePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 8
      ..style = PaintingStyle.stroke;
    canvas.drawCircle(size.center(Offset.zero), size.width / 2, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class ArcPainter extends CustomPainter {
  final double progress;
  final Color color;

  ArcPainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 12
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final startAngle = -3.14 / 2;
    final sweepAngle = 2 * 3.14 * progress;

    canvas.drawArc(
      Rect.fromCircle(center: size.center(Offset.zero), radius: size.width / 2),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
