import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/services/OfflineSyncService.dart';
import 'package:pro/services/BackgroundSyncService.dart';
import 'package:pro/debug/ContactsDebugPage.dart';
import 'package:pro/services/EmergencySmsService.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:dio/dio.dart';

class EmergencySettingsPage extends StatefulWidget {
  const EmergencySettingsPage({Key? key}) : super(key: key);

  @override
  State<EmergencySettingsPage> createState() => _EmergencySettingsPageState();
}

class _EmergencySettingsPageState extends State<EmergencySettingsPage> {
  bool _isLoading = false;
  Map<String, dynamic> _syncStatus = {};
  int _contactsCount = 0;
  String _emergencyMessage = '';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load sync status
      final dio = Dio();
      final apiConsumer = DioConsumer(dio: dio);
      final syncService = OfflineSyncService(api: apiConsumer);
      final syncStatus = await syncService.getSyncStatus();

      // Load contacts count
      final contactsCount = await OfflineContactService.getContactsCount();
      log('DEBUG - EmergencySettingsPage loaded contacts count: $contactsCount');

      // Load emergency message
      final emergencyMessage = EmergencySmsService.getEmergencyMessagePreview();

      setState(() {
        _syncStatus = syncStatus;
        _contactsCount = contactsCount;
        _emergencyMessage = emergencyMessage;
      });
    } catch (e) {
      log('Error loading settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _forceSyncContacts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dio = Dio();
      final apiConsumer = DioConsumer(dio: dio);
      final syncService = OfflineSyncService(api: apiConsumer);

      final success = await syncService.forceSyncContacts();

      if (success) {
        _showSnackBar('Contacts synced successfully!', Colors.green);
        await _loadSettings(); // Reload settings
      } else {
        _showSnackBar('Failed to sync contacts. Please try again.', Colors.red);
      }
    } catch (e) {
      log('Error syncing contacts: $e');
      _showSnackBar('Error syncing contacts: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testEmergencySMS() async {
    final confirmed = await _showConfirmationDialog(
      'Test Emergency SMS',
      'This will send a test emergency message to all your contacts. Are you sure?',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await EmergencySmsService.sendEmergencySMS();

      if (success) {
        _showSnackBar('Test emergency SMS sent successfully!', Colors.green);
      } else {
        _showSnackBar(
            'Failed to send test SMS. Please check your contacts.', Colors.red);
      }
    } catch (e) {
      log('Error sending test SMS: $e');
      _showSnackBar('Error sending test SMS: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(title),
              content: Text(content),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Confirm'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Emergency Settings'),
        backgroundColor: Colors.red[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ContactsDebugPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadSettings,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Emergency Message Section
                  _buildSectionCard(
                    title: 'Emergency Message',
                    icon: Icons.message,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _emergencyMessage,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'This message will be sent to all your emergency contacts.',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Contacts Section
                  _buildSectionCard(
                    title: 'Emergency Contacts',
                    icon: Icons.contacts,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Total Contacts: $_contactsCount',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Icon(
                            _contactsCount > 0
                                ? Icons.check_circle
                                : Icons.warning,
                            color: _contactsCount > 0
                                ? Colors.green
                                : Colors.orange,
                          ),
                        ],
                      ),
                      if (_contactsCount == 0) ...[
                        const SizedBox(height: 8),
                        Text(
                          'No emergency contacts found. Please sync your contacts.',
                          style: TextStyle(
                            color: Colors.orange[700],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Sync Status Section
                  _buildSectionCard(
                    title: 'Sync Status',
                    icon: Icons.sync,
                    children: [
                      _buildStatusRow('Last Sync',
                          _syncStatus['lastSyncFormatted'] ?? 'Never'),
                      _buildStatusRow('Needs Sync',
                          _syncStatus['needsSync'] == true ? 'Yes' : 'No'),
                      _buildStatusRow('Auto Sync', 'Every 48 hours'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Action Buttons
                  _buildActionButton(
                    label: 'Sync Contacts Now',
                    icon: Icons.sync,
                    color: Colors.blue,
                    onPressed: _forceSyncContacts,
                  ),

                  const SizedBox(height: 12),

                  _buildActionButton(
                    label: 'Test Emergency SMS',
                    icon: Icons.send,
                    color: Colors.orange,
                    onPressed: _contactsCount > 0 ? _testEmergencySMS : null,
                  ),

                  const SizedBox(height: 24),

                  // Info Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[600]),
                            const SizedBox(width: 8),
                            Text(
                              'How it works',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[800],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Contacts are automatically synced every 48 hours\n'
                          '• Emergency SMS works even without internet\n'
                          '• Your contacts are stored securely on your device\n'
                          '• The SOS button sends messages to all contacts instantly',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
