import 'dart:developer';
import 'package:flutter_sms/flutter_sms.dart';
import 'package:pro/services/OfflineContactService.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

class EmergencySmsService {
  static const String emergencyMessage = "I am in danger";

  // Send emergency SMS to all stored contacts
  static Future<bool> sendEmergencySMS() async {
    try {
      log('Starting emergency SMS sending process...');

      // Get phone numbers from local storage
      final phoneNumbers = await OfflineContactService.getPhoneNumbers();

      if (phoneNumbers.isEmpty) {
        log('No phone numbers found for emergency SMS');
        return false;
      }

      log('Found ${phoneNumbers.length} phone numbers for emergency SMS');

      // Get current user info for personalized message
      final userName = _getCurrentUserName();
      final personalizedMessage =
          userName != null ? "$emergencyMessage - $userName" : emergencyMessage;

      // Send SMS to all contacts
      final result =
          await _sendSMSToMultipleRecipients(phoneNumbers, personalizedMessage);

      if (result) {
        log('Emergency SMS sent successfully to ${phoneNumbers.length} contacts');
        await _logEmergencyEvent();
      } else {
        log('Failed to send emergency SMS');
      }

      return result;
    } catch (e) {
      log('Error sending emergency SMS: $e');
      return false;
    }
  }

  // Send SMS to multiple recipients
  static Future<bool> _sendSMSToMultipleRecipients(
      List<String> phoneNumbers, String message) async {
    try {
      // Clean and validate phone numbers
      final validNumbers = phoneNumbers
          .map((number) => _cleanPhoneNumber(number))
          .where((number) => _isValidPhoneNumber(number))
          .toList();

      if (validNumbers.isEmpty) {
        log('No valid phone numbers found');
        return false;
      }

      log('Sending SMS to ${validNumbers.length} valid numbers');

      // Send SMS using flutter_sms
      final result = await sendSMS(
        message: message,
        recipients: validNumbers,
        sendDirect: true, // Send directly without showing SMS app
      );

      log('SMS send result: $result');
      return result == 'sent';
    } catch (e) {
      log('Error in _sendSMSToMultipleRecipients: $e');

      // Fallback: try sending individually
      return await _sendSMSIndividually(phoneNumbers, message);
    }
  }

  // Fallback method: send SMS individually
  static Future<bool> _sendSMSIndividually(
      List<String> phoneNumbers, String message) async {
    try {
      log('Attempting to send SMS individually...');
      int successCount = 0;

      for (final phoneNumber in phoneNumbers) {
        try {
          final cleanNumber = _cleanPhoneNumber(phoneNumber);
          if (_isValidPhoneNumber(cleanNumber)) {
            final result = await sendSMS(
              message: message,
              recipients: [cleanNumber],
              sendDirect: true,
            );

            if (result == 'sent') {
              successCount++;
              log('SMS sent successfully to $cleanNumber');
            } else {
              log('Failed to send SMS to $cleanNumber: $result');
            }
          }
        } catch (e) {
          log('Error sending SMS to $phoneNumber: $e');
        }

        // Small delay between sends
        await Future.delayed(const Duration(milliseconds: 500));
      }

      final success = successCount > 0;
      log('Individual SMS sending completed. Success count: $successCount/${phoneNumbers.length}');
      return success;
    } catch (e) {
      log('Error in _sendSMSIndividually: $e');
      return false;
    }
  }

  // Clean phone number (remove spaces, dashes, etc.)
  static String _cleanPhoneNumber(String phoneNumber) {
    return phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
  }

  // Validate phone number format
  static bool _isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return false;

    // Basic validation: should contain only digits and optional + at start
    final regex = RegExp(r'^\+?[1-9]\d{1,14}$');
    return regex.hasMatch(phoneNumber);
  }

  // Get current user name for personalized message
  static String? _getCurrentUserName() {
    try {
      return CacheHelper.getData(key: 'userName') ??
          CacheHelper.getData(key: 'user_name') ??
          CacheHelper.getData(key: 'name') ??
          CacheHelper.getData(key: ApiKey.name);
    } catch (e) {
      log('Error getting user name: $e');
      return null;
    }
  }

  // Log emergency event for tracking
  static Future<void> _logEmergencyEvent() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final userId = CacheHelper.getData(key: ApiKey.userId);

      // Save emergency event to local storage for later sync
      final emergencyEvents =
          CacheHelper.getData(key: 'emergency_events') ?? [];
      if (emergencyEvents is List) {
        emergencyEvents.add({
          'timestamp': timestamp,
          'userId': userId,
          'message': emergencyMessage,
          'contactsCount': await OfflineContactService.getContactsCount(),
        });

        await CacheHelper.saveData(
            key: 'emergency_events', value: emergencyEvents);
        log('Emergency event logged locally');
      }
    } catch (e) {
      log('Error logging emergency event: $e');
    }
  }

  // Check if SMS permission is available
  static Future<bool> checkSMSPermission() async {
    try {
      // Try to send a test (this will prompt for permission if needed)
      // We don't actually send anything, just check if the method is available
      return true; // flutter_sms handles permissions internally
    } catch (e) {
      log('SMS permission check failed: $e');
      return false;
    }
  }

  // Get emergency message preview
  static String getEmergencyMessagePreview() {
    final userName = _getCurrentUserName();
    return userName != null
        ? "$emergencyMessage - $userName"
        : emergencyMessage;
  }
}
