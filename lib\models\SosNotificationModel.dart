class SosNotificationModel {
  final double latitude;
  final double longitude;
  final String message;
  final String travelerId;
  final String travelerName;
  final DateTime timestamp;
  final List<String>?
      supporterIds; // List of supporter IDs who should receive this notification

  SosNotificationModel({
    required this.latitude,
    required this.longitude,
    required this.message,
    required this.travelerId,
    required this.travelerName,
    required this.timestamp,
    this.supporterIds,
  });

  factory SosNotificationModel.fromJson(Map<String, dynamic> json) {
    // Extract supporter IDs from JSON
    List<String>? supporterIds;
    if (json['supporterIds'] != null) {
      if (json['supporterIds'] is List) {
        supporterIds = List<String>.from(
          (json['supporterIds'] as List).map((id) => id.toString()),
        );
      }
    }

    return SosNotificationModel(
      latitude: json['latitude'] is String
          ? double.parse(json['latitude'])
          : json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude'] is String
          ? double.parse(json['longitude'])
          : json['longitude']?.toDouble() ?? 0.0,
      message: json['message'] ?? 'SOS Alert!',
      travelerId: json['travelerId'] ?? '',
      travelerName: json['travelerName'] ?? 'Unknown Traveler',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      supporterIds: supporterIds,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'latitude': latitude,
      'longitude': longitude,
      'message': message,
      'travelerId': travelerId,
      'travelerName': travelerName,
      'timestamp': timestamp.toIso8601String(),
      'sendToSupportersOnly': true, // Always send only to supporters
    };

    // Add supporter IDs if available
    if (supporterIds != null) {
      data['supporterIds'] = supporterIds;
    }

    return data;
  }
}

class SosResponse {
  final bool success;
  final String message;
  final String? messageId;
  final int? recipientCount;
  final List<String>?
      supporterIds; // List of supporter IDs who received the notification

  SosResponse({
    required this.success,
    required this.message,
    this.messageId,
    this.recipientCount,
    this.supporterIds,
  });

  factory SosResponse.fromJson(Map<String, dynamic> json) {
    // Extract supporter IDs from JSON
    List<String>? supporterIds;
    if (json['supporterIds'] != null) {
      if (json['supporterIds'] is List) {
        supporterIds = List<String>.from(
          (json['supporterIds'] as List).map((id) => id.toString()),
        );
      }
    }

    return SosResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? 'Unknown response',
      messageId: json['messageId'],
      recipientCount: json['recipientCount'],
      supporterIds: supporterIds,
    );
  }
}
